.top-header-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 66rpx;
  position: relative;
  z-index: 999;

  .header-item-center {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    font-size: 36rpx;
    color: #fff;
    font-weight: bold;
  }
  .header-item-left {
    display: flex;
    align-items: center;
  }
  .header-item-right {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    // 确保搜索按钮不会超出安全区域
    .search {
      width: 40rpx;
      height: 40rpx;
      flex-shrink: 0; // 防止被压缩
    }
  }

  .exam-name {
    display: flex;
    align-items: center;
    font-size: 24rpx;
    color: #919499;
    line-height: 44rpx;

    .icon {
      width: 32rpx;
      transform: translateY(2rpx);
      margin-left: 2rpx;
    }
  }
}

.exam-box {
  position: fixed;
  left: 0;
  z-index: 222;
  display: flex;
  align-items: center;
  padding-left: 40rpx;
  height: 66rpx;
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #fff;
  // font-weight: bold;
  // line-height: 44rpx;

  .icon {
    width: 32rpx;
    transform: translateY(2rpx);
    margin-left: 2rpx;
    height: 32rpx;
  }
}

.status-box {
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  background-color: transparent;
  // padding-left: 32rpx;
  box-sizing: border-box;
  padding-bottom: 12rpx;
  &.bgf {
    background-color: #fff;
  }
}

.flex-c {
  display: flex;
  align-items: center;
}

.status-no {
  .container {
    padding: 0;
  }
}

.tab-list {
  display: flex;
  align-items: center;
  padding-left: 32rpx;
  .tab-list-item {
    font-size: 28rpx;
    color: #fff;
    margin-right: 40rpx;
    padding-bottom: 20rpx;
    // padding: 20rpx 0;
    &.active {
      font-size: 32rpx;
      position: relative;
      font-weight: bold;
      &::after {
        position: absolute;
        display: block;
        content: " ";
        width: 32rpx;
        height: 6rpx;
        background-color: rgba(255, 235, 0, 1);
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        border-radius: 5rpx;
      }
    }
  }
}

.bgf {
  .tab-list-item {
    color: #666;
    &.active {
      color: #22242e;
      font-weight: bold;
    }
  }
  .header-item-center {
    color: rgba(49, 52, 54, 1);
  }
}
