/**
 * 弹窗底部操作栏组件
 * 提供重置和确定按钮的通用底部操作栏
 */
Component({
  options: {
    addGlobalClass: true,
    multipleSlots: false,
  },

  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示重置按钮
    showResetBtn: {
      type: Boolean,
      value: true,
    },
    // 重置按钮文本
    resetBtnText: {
      type: String,
      value: "重置",
    },
    // 确定按钮文本
    confirmBtnText: {
      type: String,
      value: "确定",
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: "",
    },
    isEducation: {
      type: Boolean,
      value: false,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    isSynchronous: false,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    changeSynchro() {
      this.setData({
        isSynchronous: !this.data.isSynchronous,
      })
    },
    /**
     * 重置按钮点击事件
     */
    handleReset() {
      this.triggerEvent("reset", {}, {})
    },

    /**
     * 确定按钮点击事件
     */
    handleConfirm() {
      this.triggerEvent("confirm", {}, {})
    },
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例进入页面节点树时执行
    },

    detached() {
      // 组件实例被从页面节点树移除时执行
    },
  },
})
