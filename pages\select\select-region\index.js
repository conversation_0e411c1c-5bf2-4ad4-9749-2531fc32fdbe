const ROUTER = require("@/services/mpRouter")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const APP = getApp()

const {
  setSelectedRegionsCache,
  getSelectedRegionsCache,
} = require("../../../utils/cache/regionCache")

Page({
  data: {
    show_white: true,
    // Tab类型，用于区分不同来源的地区选择
    tabType: "announcement", // 默认为公告Tab
    // 已选择的地区列表
    selectedRegions: [],
    showPopupFilterMenu: false,
  },

  async onLoad(options) {
    await APP.checkLoadRequest()
    // 获取Tab类型参数
    const { tabType = "announcement" } = options

    this.setData({
      tabType: tabType,
      isLogin: APP.getIsLogin(),
    })

    console.log("地区选择页面接收到Tab类型:", tabType)

    // 加载对应Tab的地区缓存
    const cachedRegions = getSelectedRegionsCache(tabType)
    const userList = APP?.globalData?.serverConfig?.user_exam_record_list || []
    // 合并两个数组并过滤重复项（以 districtId 为唯一标识）
    const mergedRegions = [...cachedRegions]
    userList.forEach((userRegion) => {
      const exists = cachedRegions.some(
        (cached) => cached.key === userRegion.key
      )
      if (!exists) {
        mergedRegions.push(userRegion)
      }
    })
    // 使用合并后的数据
    const finalRegions = mergedRegions
    if (finalRegions && finalRegions.length > 0) {
      this.setData({
        selectedRegions: finalRegions,
        showPopupFilterMenu: true,
      })
      console.log(`加载${tabType}Tab的地区缓存:`, finalRegions)
    }
  },

  onShow() {},
  /**
   * 返回上一页
   */
  backPage() {
    wx.navigateBack()
  },

  /**
   * 完成选择
   */
  async confirmSelection(e) {
    const { tempSelected } = e.detail
    console.log(e, "122222222222222222222")
    // 保存选中的地区到对应Tab的缓存
    setSelectedRegionsCache(tempSelected, this.data.tabType)
    console.log(this.data.isLogin, "是否登录")
    if (this.data.isLogin) {
      const region_list = tempSelected.map((item) => item.key)
      await UTIL.request(API.saveEnterExamRecord, {
        region_list,
      })
    }
    // 返回上一页
    wx.navigateBack()
  },

  /**
   * 初始化已选择地区的状态同步
   */
  syncSelectedRegionsWithLists() {
    const { selectedRegions, districtList } = this.data

    if (districtList.length > 0) {
      // 根据已选择的地区更新区县列表的选中状态
      const updatedDistrictList = districtList.map((district) => {
        const isSelected = selectedRegions.some(
          (region) =>
            region.type === "district" && region.districtId === district.code
        )
        return Object.assign({}, district, { selected: isSelected })
      })

      this.setData({
        districtList: updatedDistrictList,
      })

      // 更新省市统计数字
      this.updateProvinceAndCityCount()
    }
  },

  onPageScroll(e) {
    if (e.scrollTop > 0) {
      if (!this.data.show_white) {
        this.setData({ show_white: true })
      }
    } else {
      if (this.data.show_white) {
        this.setData({ show_white: false })
      }
    }
  },

  onReachBottom() {},
})
