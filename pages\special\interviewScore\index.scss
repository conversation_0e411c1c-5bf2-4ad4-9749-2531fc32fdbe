page {
  background: #f7f8fa;
}

// vant弹窗样式 - 确保覆盖底部固定区域
.van-popup {
  z-index: 10000 !important; // 比底部固定区域的999更高
}

.van-overlay {
  z-index: 9999 !important; // 遮罩层也需要高z-index
}

// vant选择器样式
.column-area {
  .van-picker-column__item {
    font-size: 28rpx;
    font-weight: 400;
    color: #999999;
  }

  view .active-item {
    color: #333333;
  }
}

.top-area {
  .van-picker__title {
    font-size: 28rpx;
    font-weight: 500;
    color: #333333;
  }

  .van-picker__confirm {
    font-size: 28rpx;
    font-weight: 400;
    color: #cd3023;
  }

  .van-picker__cancel {
    font-size: 28rpx;
    font-weight: 400;
    color: #999999;
  }
}
.big-data {
  min-height: 100vh;
  position: relative;

  // 固定头部导航栏
  .fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: #ffffff;
    z-index: 99999;
    border-bottom: 1rpx solid #f0f0f0;
    transform: rotateZ(360deg);

    // 默认隐藏
    display: none;

    &.show {
      display: block !important;
    }

    &.hide {
      display: none !important;
    }

    .header-content {
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 32rpx;

      .header-left {
        width: 100rpx;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        height: 44px;

        .back-icon {
          width: 24rpx;
          height: 24rpx;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;

          // 用CSS绘制黑色返回箭头（向左）
          &::before {
            content: "";
            width: 16rpx;
            height: 16rpx;
            border: 0;
            border-left: 4rpx solid #000000;
            border-bottom: 4rpx solid #000000;
            transform: rotate(45deg);
          }
        }
      }

      .header-title {
        flex: 1;
        text-align: center;
        font-size: 36rpx;
        font-weight: bold;
        color: #000000;
        line-height: 44px;
      }

      .header-right {
        width: 100rpx;
      }
    }
  }
  .back-btn {
    position: absolute;
    top: 112rpx;
    left: 40rpx;
    width: 40rpx;
    height: 40rpx;
    z-index: 1000; // 提高层级，确保在所有内容之上
    pointer-events: auto; // 确保可以接收点击事件
  }
  .top-bg {
    width: 100%;
    height: 520rpx;
    position: relative;
    z-index: 0;
    .top-area {
      padding: 104rpx 40rpx 40rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      z-index: 1; // 设置较低的层级
      .top-title-img {
        width: 328rpx;
        height: 64rpx;
      }
      .title {
        font-size: 36rpx;
        font-weight: bold;
        color: #ffffff;
        margin-top: 4rpx;
      }
      .select-one {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 64rpx;
        margin-bottom: 26rpx;
      }
      .select-item {
        width: 322rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(64rpx);
        border-radius: 12rpx;
        border: 1rpx solid rgba(255, 255, 255, 0.4);
        transform: rotateZ(360deg);
        padding: 18rpx 20rpx 18rpx 24rpx;
        box-sizing: border-box;
        .text {
          color: #22242e;
          font-size: 26rpx;
        }
        .arrow {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }
    .bg-img {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: -1;
    }
  }

  .main-content {
    width: 100%;
    margin-top: -78rpx;
    background: #ffffff;
    border-radius: 24rpx 24rpx 0 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-bottom: 16rpx;
    position: relative;
    z-index: 200;

    // Tab导航区域样式
    .tab-container {
      position: relative;
      width: 100%;
      background: #ffffff;
      border-bottom: 2rpx solid #ebecf0;

      .tab-scroll {
        width: 100%;
        white-space: nowrap;

        .tab-list {
          display: inline-flex;
          align-items: center;

          .tab-item {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 34rpx 40rpx 22rpx 40rpx;
            font-size: 28rpx;
            color: #666666;
            position: relative;
            white-space: nowrap;
            flex-shrink: 0;

            &.active {
              color: #22242e;
              font-weight: bold;
              font-size: 32rpx;

              &::after {
                content: "";
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 108rpx;
                height: 6rpx;
                background: #e60003;
              }
            }
          }
        }
      }
    }

    // 内容区域样式
    .content-container {
      flex: 1;
      width: 100%;

      .content-area {
        padding: 36rpx 32rpx 40rpx 32rpx;
        box-sizing: border-box;

        // 招聘公告标题
        .announcement-title {
          margin-bottom: 32rpx;
          .text {
            font-size: 26rpx;
            color: #5f7e95;
            vertical-align: middle;
          }
          .arrow {
            display: inline-block;
            width: 32rpx;
            height: 32rpx;
            margin-left: 4rpx;
            vertical-align: middle;
          }
        }

        .data-flex-box {
          display: flex;
          padding: 32rpx 0;
          box-sizing: border-box;
          background: rgba(247, 248, 250, 0.5);
          border-radius: 16rpx;
          margin-bottom: 32rpx;
          .flex-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            .num {
              font-size: 40rpx;
              color: #3c3d42;
              font-family: "DINBold";
              font-weight: 500;
            }
            .cred {
              color: #e60003 !important;
            }
            .corange {
              color: #ff6a4d !important;
            }
            .text {
              color: #3c3d42;
              font-size: 24rpx;
              margin-top: 24rpx;
            }
            &::after {
              content: "";
              width: 2rpx;
              height: 48rpx;
              position: absolute;
              right: 0;
              top: 30rpx;
              background: #ebecf0;
            }
            &:last-of-type {
              &::after {
                display: none;
              }
            }
          }
        }

        .data-desc {
          position: relative;

          &.collapsed {
            .data-desc-wrapper {
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
              overflow: hidden;
              padding-right: 80rpx; // 为按钮预留空间
            }

            > .expand-btn {
              position: absolute;
              right: 0;
              bottom: 4rpx;
              background: linear-gradient(
                to right,
                transparent,
                #ffffff 30%,
                #ffffff
              );
              padding-left: 16rpx;
            }
          }

          &.expanded {
            .data-desc-wrapper {
              display: block;
            }
          }

          .data-desc-wrapper {
            font-size: 24rpx;
            line-height: 1.6;
            color: #919499;

            .data-desc-text {
              font-size: 24rpx;
              color: #919499;
              line-height: 1.6;
            }

            .expand-btn.inline {
              display: inline-flex;
              align-items: center;
              margin-left: 8rpx;
              vertical-align: baseline;
            }
          }

          .expand-btn {
            display: inline-flex;
            align-items: center;
            cursor: pointer;

            .blue-text {
              font-size: 24rpx;
              color: #448aff;
            }

            .arrow-icon {
              width: 24rpx;
              height: 24rpx;
              margin-left: 4rpx;
              transition: transform 0.3s ease;

              &.rotated {
                transform: rotate(180deg);
              }
            }
          }
        }
      }
    }
  }

  .job-search {
    padding: 40rpx 32rpx;
    box-sizing: border-box;
    background: #ffffff;
    margin-top: 16rpx;
    .title {
      font-size: 32rpx;
      color: #22242e;
      font-weight: bold;
    }
    .search-box {
      position: relative;
      margin-top: 32rpx;
      width: 100%;
      background: #ff6a4d;
      border-radius: 24rpx;
      padding: 8rpx;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      z-index: 1;
      .bg {
        width: 100%;
        height: 604rpx;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 0;
      }
      .top-area {
        padding: 0 16rpx;
        display: flex;
        align-items: center;
        margin-bottom: 24rpx;
        .icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 8rpx;
        }
        .text {
          color: #ffffff;
          font-size: 28rpx;
          font-weight: 500;
        }
      }
      .white-box {
        position: relative;
        z-index: 2;
        flex: 1;
        width: 100%;
        background: #ffffff;
        border-radius: 16rpx;
        padding: 40rpx;
        box-sizing: border-box;
        .select-item {
          background: #f7f8fa;
          border-radius: 16rpx;
          border: 1rpx solid #ebecf0;
          transform: rotateZ(360deg);
          margin-bottom: 24rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 26rpx 24rpx 26rpx 32rpx;
          box-sizing: border-box;
          &:last-of-type {
            margin-bottom: 0;
          }
          .left {
            display: flex;
            align-items: center;
            color: #3c3d42;
            font-size: 26rpx;
            flex: 1;
            .sp5 {
              width: 130rpx;
              display: flex;
              justify-content: space-between;
              align-items: center;
              line-height: 44rpx;
            }
          }
          .right {
            width: 32rpx;
            height: 32rpx;
          }

          // 输入框样式
          &.input-item {
            .left {
              .position-input {
                flex: 1;
                font-size: 26rpx;
                color: #3c3d42;
                background: transparent;
                border: none;
                outline: none;

                .input-placeholder {
                  color: #919499;
                  font-size: 26rpx;
                }
              }
            }
          }
        }
        .search-btn {
          margin-top: 40rpx;
          width: 100%;
          height: 84rpx;
          background: #ec3e33;
          border-radius: 16rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #ffffff;
          font-size: 28rpx;
          font-weight: 500;
          .icon {
            width: 32rpx;
            height: 32rpx;
            margin-right: 8rpx;
          }
        }
        .delete-text {
          width: 100%;
          text-align: center;
          margin-top: 32rpx;
          color: #ec3e33;
          font-size: 28rpx;
          font-weight: 500;
        }
      }
    }
  }

  // 查询结果表格样式
  .search-result-table {
    background: #ffffff;
    margin-top: 16rpx;
    padding: 32rpx;
    box-sizing: border-box;
    border: 1rpx solid #ebecf0;
    transform: rotateZ(360deg);
    border-radius: 16rpx;

    // 表格头部
    .table-header {
      display: flex;
      align-items: center;
      background: #fafbfc;
      border: 1rpx solid #ebecf0;
      transform: rotateZ(360deg);
      border-radius: 8rpx;
      padding: 24rpx 0;
      margin-bottom: 0;

      .header-item {
        font-size: 26rpx;
        color: #3c3d42;
        font-weight: 500;
        text-align: center;
        padding: 0 16rpx;
        border-right: 1rpx solid #ebecf0;
        transform: rotateZ(360deg);
        &:last-child {
          border-right: none;
        }

        &.header-position {
          flex: 4;
          text-align: center;
        }

        &.header-unit {
          flex: 4;
          text-align: center;
        }

        &.header-ratio {
          flex: 3;
          text-align: center;
        }
      }
    }

    // 表格主体
    .table-body {
      .table-row {
        display: flex;
        align-items: center;
        border: 1rpx solid #ebecf0;
        transform: rotateZ(360deg);
        border-top: none;

        &:last-child {
          border-bottom-left-radius: 8rpx;
          border-bottom-right-radius: 8rpx;
        }

        .table-item {
          padding: 32rpx 16rpx;
          border-right: 1rpx solid #ebecf0;
          transform: rotateZ(360deg);
          &:last-child {
            border-right: none;
          }

          &.table-position {
            flex: 4;
            text-align: center;

            .position-name {
              font-size: 26rpx;
              color: #448aff;
              line-height: 1.4;
              word-wrap: break-word;
            }
          }

          &.table-unit {
            flex: 4;
            text-align: center;

            .unit-name {
              font-size: 26rpx;
              color: #3c3d42;
              line-height: 1.4;
              word-wrap: break-word;
            }
          }

          &.table-ratio {
            flex: 3;
            text-align: center;

            .ratio-value {
              font-size: 26rpx;
              color: #3c3d42;
              font-weight: 500;
            }
          }
        }
      }
    }
  }

  .bottom-box {
    padding: 14rpx 32rpx 14rpx 42rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      display: flex;
      align-items: center;
      .icon-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 40rpx;
        .icon-img {
          width: 40rpx;
          height: 40rpx;
          margin-bottom: 4rpx;
        }
        .icon-text {
          color: #919499;
          font-size: 20rpx;
        }
      }
    }
    .right-btn {
      width: 400rpx;
      height: 84rpx;
      background: #ec3e33;
      border-radius: 16rpx;
      font-size: 28rpx;
      color: #ffffff;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

// 动画效果
@keyframes slideIn {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 60rpx;
    opacity: 1;
  }
}

.w100 {
  width: 100% !important;
}
.area-box {
  margin: 24rpx;
  padding: 24rpx;
  background: #ffffff;
  border-radius: 12rpx;

  .title {
    font-size: 32rpx;
    font-weight: 500;
    color: #3c3d42;
    margin-bottom: 24rpx;
  }

  .baoming {
    width: 100%;
    margin-top: 24rpx;
  }
}

.column-area {
  .van-picker-column__item {
    font-size: 30rpx;
    font-weight: 400;
    color: #313436;
  }

  view .active-item {
    font-weight: 500;
    color: #313436;
    font-size: 32rpx;
  }
}

.top-area {
  .van-picker__title {
    font-size: 32rpx;
    font-weight: 500;
    color: #313436;
  }

  .van-picker__confirm {
    font-size: 28rpx;
    font-weight: 400;
    color: #e60003;
  }

  .van-picker__cancel {
    font-size: 28rpx;
    font-weight: 400;
    color: #c2c5cc;
  }
}
.van-picker__toolbar {
  border-bottom: 2rpx solid #ebecf0;
}
