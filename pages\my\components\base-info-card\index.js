const ROUTER = require("@/services/mpRouter")
const APP = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    baseInfo: {
      type: Object,
      value: {},
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 通用选择器状态
    selectorShow: false,
    selectorType: "",
    selectorTitle: "请选择",
    selectorValue: [], // 存储选择器的回显值
    currentField: "",
    waitingForReady: false, // 是否正在等待选择器准备完成

    incompleteTips: "6项待完善",

    formList: [
      {
        title: "报考地区",
        field: "region",
        required: true,
        text: "",
        value: "",
      },
      {
        title: "出生日期",
        field: "birthday",
        required: false,
        text: "",
        value: "",
        dateIndexes: [], // 存储日期选择器的索引，用于回显
      },
      {
        title: "性别",
        field: "gender",
        required: false,
        text: "",
        value: "", // 存储选项的value值，如：male/female
      },
      {
        title: "政治面貌",
        field: "political",
        required: false,
        text: "",
        value: "", // 存储选项的value值，如：party_member
      },
      {
        title: "民族",
        field: "nation",
        required: false,
        text: "",
        value: "", // 存储选项的value值，如：han
      },
      {
        title: "户籍",
        field: "household",
        required: false,
        text: "",
        value: "",
        selectedIndexes: [], // 存储地区选择器的索引数组，如：[0,1,2]
      },
    ],
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 根据value值获取对应的索引（用于单选类型的回显）
     */
    getIndexByValue(type, value) {
      const dataMap = {
        gender: APP.globalData?.serverConfig?.gender_list || [],
        politics: APP.globalData?.serverConfig?.politics_face_list || [],
        nation: APP.globalData?.serverConfig?.nation_list || [],
        household: APP.globalData?.serverConfig?.regionData || [],
      }

      const data = dataMap[type]
      if (!data || !value) return []

      if (type === "household") {
        // 确保value是有效数组，且不含无效id
        if (!Array.isArray(value) || value.some((id) => !id)) return []

        const [provinceId, cityId, districtId] = value

        const provinceIndex = data.findIndex((p) => p.id == provinceId)
        if (provinceIndex === -1) return []

        const province = data[provinceIndex]
        const cityIndex = province.child?.findIndex((c) => c.id == cityId) ?? -1
        if (cityIndex === -1) return []

        const city = province.child[cityIndex]
        const districtIndex =
          city.child?.findIndex((d) => d.id == districtId) ?? -1
        if (districtIndex === -1) return []

        return [provinceIndex, cityIndex, districtIndex]
      } else {
        const index = data.findIndex((item) => item.id == value)
        return index >= 0 ? [index] : []
      }
    },

    /**
     * 表单项点击事件
     */
    onFormItemTap(e) {
      const { field } = e.currentTarget.dataset
      let selectorType = "region"
      let selectorTitle = "请选择"
      let selectorValue = []

      const formList = this.data.formList
      const fieldData = formList.find((item) => item.field === field)

      // 根据字段类型设置选择器类型和回显值
      switch (field) {
        case "region":
          ROUTER.navigateTo({
            path: "/pages/select/select-region/index",
          })
          return
        case "household":
          selectorType = "region"
          selectorTitle = "请选择户籍"
          // 地区选择器用索引数组回显
          if (fieldData && fieldData.value) {
            selectorValue = this.getIndexByValue("household", fieldData.value)
          }
          break
        case "birthday":
          selectorType = "date"
          selectorTitle = "请选择出生日期"
          // 日期选择器用索引数组回显
          if (
            fieldData &&
            fieldData.dateIndexes &&
            fieldData.dateIndexes.length > 0
          ) {
            selectorValue = fieldData.dateIndexes
          } else {
            // 没有回显值时，计算今天的索引，避免跳动
            const today = new Date()
            const currentYear = today.getFullYear()
            const currentMonth = today.getMonth() + 1
            const currentDay = today.getDate()

            const yearIndex = currentYear - 1950 // 今年的索引位置
            const monthIndex = currentMonth - 1 // 月份索引从0开始
            const dayIndex = currentDay - 1 // 日期索引从0开始

            selectorValue = [yearIndex, monthIndex, dayIndex]
          }
          break
        case "gender":
          selectorType = "gender"
          selectorTitle = "请选择性别"
          // 其他选择器用value回显，需要转换为索引
          if (fieldData && fieldData.value) {
            selectorValue = this.getIndexByValue("gender", fieldData.value)
          }
          break
        case "political":
          selectorType = "politics"
          selectorTitle = "请选择政治面貌"
          // 其他选择器用value回显，需要转换为索引
          if (fieldData && fieldData.value) {
            selectorValue = this.getIndexByValue("politics", fieldData.value)
          }
          break
        case "nation":
          selectorType = "nation"
          selectorTitle = "请选择民族"
          // 其他选择器用value回显，需要转换为索引
          if (fieldData && fieldData.value) {
            selectorValue = this.getIndexByValue("nation", fieldData.value)
          }
          break
        default:
          return
      }

      // 先设置选择器数据，但不显示弹窗
      this.setData({
        selectorType,
        selectorTitle,
        selectorValue,
        currentField: field,
        waitingForReady: true, // 标记正在等待ready事件
      })

      // 不再使用延迟显示，而是等待ready事件
      // setTimeout(() => {
      //   this.setData({
      //     selectorShow: true,
      //   })
      // }, delay)
    },

    /**
     * 选择器准备完成事件
     */
    onSelectorReady(e) {
      // 只有在等待ready事件时才显示弹窗
      if (this.data.waitingForReady) {
        this.setData({
          selectorShow: true,
          waitingForReady: false,
        })
      }
    },

    /**
     * 关闭选择器
     */
    onSelectorClose() {
      this.setData({
        selectorShow: false,
      })
    },

    /**
     * 确认选择
     */
    onSelectorConfirm(e) {
      const { type, text, value, date, index } = e.detail
      console.log(text, value, index, "88888888")
      const currentField = this.data.currentField

      // 更新对应字段的值
      const formList = [...this.data.formList]
      const fieldIndex = formList.findIndex(
        (item) => item.field === currentField
      )

      if (fieldIndex !== -1) {
        let displayValue = text

        // 根据不同字段类型保存对应的回显数据
        if (currentField === "household") {
          // 户籍：地区选择器，保存索引数组用于回显
          formList[fieldIndex].text = displayValue
          if (index && index.length > 0) {
            formList[fieldIndex].selectedIndexes = index
          }
          if (value && value.length > 0) {
            formList[fieldIndex].value = value
          }
        } else if (currentField === "birthday") {
          // 出生日期：日期选择器，保存索引数组和标准格式
          formList[fieldIndex].text = displayValue // 显示格式：1998年1月2日
          formList[fieldIndex].value = date // 存储标准格式：1998-01-02
          if (index && index.length > 0) {
            formList[fieldIndex].dateIndexes = index
          }
        } else if (["gender", "political", "nation"].includes(currentField)) {
          // 性别、政治面貌、民族：单选，保存value值用于回显
          formList[fieldIndex].text = displayValue
          if (value) {
            formList[fieldIndex].value = value
          }
        } else {
          // 其他字段正常处理
          formList[fieldIndex].text = displayValue
        }
      }

      // 重新计算待完善项数
      const incompleteCount = formList.filter((item) => !item.value).length
      const incompleteTips =
        incompleteCount > 0 ? `${incompleteCount}项待完善` : "已完善"

      this.setData({
        formList,
        incompleteTips,
        selectorShow: false,
      })

      // 通知父页面数据发生变化
      this.triggerEvent("dataChange", {
        formList: formList,
      })

      // 统计本卡片待完善项数（总项数-已填项数，与必填无关）
      this.countIncomplete()
    },

    /**
     * 统计本卡片待完善项数（户籍只看text，性别/民族等value为'0'也算未填）
     */
    countIncomplete() {
      const formList = this.data.formList
      let count = 0
      formList.forEach((item) => {
        if (item.field === "household") {
          // 户籍特殊，只看text
          if (!item.text || String(item.text).trim().length < 1) count++
        } else if (["gender", "nation"].includes(item.field)) {
          // 性别/民族 value为'0'也算未填
          if (
            item.value === undefined ||
            item.value === null ||
            (typeof item.value === "string" &&
              (item.value.trim().length < 1 || item.value === "0")) ||
            (Array.isArray(item.value) && item.value.length < 1)
          ) {
            count++
          }
        } else {
          // 其他项
          if (
            item.value === undefined ||
            item.value === null ||
            (typeof item.value === "string" && item.value.trim().length < 1) ||
            (Array.isArray(item.value) && item.value.length < 1)
          ) {
            count++
          }
        }
      })
      this.setData({
        incompleteTips: count > 0 ? `${count}项待完善` : "已完善",
      })
      this.triggerEvent("incompleteCount", { count })
    },
    // 保留原有方法兼容性
    onRegionSelectorClose() {
      this.onSelectorClose()
    },

    onRegionSelectorConfirm(e) {
      this.onSelectorConfirm(e)
    },
  },
  observers: {
    baseInfo(newVal) {
      if (!newVal) return
      // 复制formList
      console.log(newVal, "======")
      const formList = this.data.formList.map((item) => {
        const field = item.field
        let newItem = { ...item }
        // 处理不同字段的回显
        switch (field) {
          case "region":
            newItem.text =
              newVal.enter_exam_record?.map((item) => item.text)?.join(",") ||
              ""
            // 修正：value为字符串，单个key直接赋值，多个key逗号拼接，无内容为""
            if (
              Array.isArray(newVal.enter_exam_record) &&
              newVal.enter_exam_record.length > 0
            ) {
              newItem.value = newVal.enter_exam_record.map((item) => item.key)
            } else {
              newItem.value = ""
            }
            break
          case "birthday":
            newItem.text = newVal.birth_date?.text || ""
            newItem.value = newVal.birth_date?.value || ""
            // 计算日期索引
            if (newItem.value) {
              const [year, month, day] = newItem.value.split("-").map(Number)
              if (year && month && day) {
                newItem.dateIndexes = [year - 1950, month - 1, day - 1]
              } else {
                newItem.dateIndexes = []
              }
            } else {
              newItem.dateIndexes = []
            }
            break
          case "gender":
            newItem.text = newVal.gender?.text || ""
            newItem.value = newVal.gender?.value || ""
            break
          case "political":
            newItem.text = newVal.politics_face?.text || ""
            newItem.value = newVal.politics_face?.value || ""
            break
          case "nation":
            newItem.text = newVal.nation?.text || ""
            newItem.value = newVal.nation?.value || ""
            break
          case "household":
            newItem.text = newVal.domicile?.text || ""
            newItem.value = [
              newVal.domicile.domicile_province,
              newVal.domicile.domicile_city,
              newVal.domicile.domicile_district,
            ]
            break
          default:
            break
        }
        return newItem
      })
      // 重新计算待完善项数
      const incompleteCount = formList.filter((item) => !item.value).length
      const incompleteTips =
        incompleteCount > 0 ? `${incompleteCount}项待完善` : "已完善"
      this.setData({
        formList,
        incompleteTips,
      })

      // 统计本卡片待完善项数（总项数-已填项数，与必填无关）
      this.countIncomplete()
    },
  },
})
