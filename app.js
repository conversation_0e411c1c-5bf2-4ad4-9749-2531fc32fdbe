const CONFIG = require("./config/config")
const API = require("./config/api")
const UTIL = require("./utils/util")
const ROUTER = require("./services/mpRouter")
const BASE_CACHE = require("@/utils/cache/baseCache")
const USER_CACHE = require("@/utils/cache/userCache")
const { toCmdUnitKey } = require("@/services/cmdManager")
const COS = require("@/static/plugins/cos-wx-sdk-v5.min.js")
const { previewFile } = require("./utils/file")
// 创建一个缓存对象用于存储 promise，模拟单例模式
const promiseCache = new Map()

// app.js
App({
  toCmdUnitKey,
  onLaunch(options) {
    console.log(["onLaunch进入：", options])

    this.globalData.launchOptions = options
    if (options.query?.rcode) {
      this.globalData.rcode = options.query?.rcode
    }

    // 监听内存警告，并且上报
    wx.onMemoryWarning(function (res) {
      console.log("onMemoryWarningReceive", res)
      wx.reportEvent("event_memory_warning", { pages: getCurrentPages() })
      wx.rea
    })

    // 延迟下拉数据，避免config请求获取不到token
    setTimeout(() => {
      this.checkLoadRequest()
    })
    // 注释，测试加载速度
    // this.checkForUpdates()
  },
  // 监听小程序全局显示
  async onShow(options) {
    let list = [
      1007,
      1008,
      1014,
      1031,
      1036,
      1037,
      1043,
      1044,
      1045,
      1046,
      1047,
      1048,
      1049,
      1058,
      1073,
      1074,
      1091,
      1096,
    ]
    if (list.indexOf(options.scene) === -1) {
      console.log("无效进入场景", options.scene)
      return
    }
    await this.updateCampusAndProvinceSettings(options.query)
  },

  /**
   * 更新校区和省份设置
   * @param {Object} query - 包含校区ID和省份key的查询参数
   * @returns {boolean|undefined} - 返回false表示未执行更新，true表示设置未变化，undefined表示已执行更新
   * @description 根据传入的查询参数更新全局的校区和省份设置，当参数与缓存不一致时会触发配置初始化
   */
  async updateCampusAndProvinceSettings(query) {
    let provinceList = this.globalData.serverConfig?.province_list
    const queryCampusId = Number(query.campus_id)
    const queryProvinceKey = query.province

    // 没有CampusId就不执行
    if (!queryCampusId && !queryProvinceKey) {
      return false
    }

    console.log("onShow 修改选择设置", query)
    // 没有数据时去请求
    if (!provinceList || provinceList?.length <= 0) {
      await this.checkLoadRequest()
    }
    provinceList = this.globalData.serverConfig?.province_list

    const { campus, province } = BASE_CACHE.getBaseCache() // 校区 考试方向 省份
    const chacheCampusId = campus?.id

    // 当前校区id与入口参数id不一致时执行
    if (
      JSON.stringify(queryCampusId) !== JSON.stringify(chacheCampusId) ||
      queryProvinceKey !== province?.key
    ) {
      this.globalData.launchOptions.oldQuery = this.globalData.launchOptions.query
      this.globalData.launchOptions.query = query
      this.globalData.launchOptions.onShowChange = true
      this.initCommonConfiguration(provinceList, query)
      return
    }
    return true
  },
  //判断版本是否更新
  checkForUpdates() {
    const updateManager = wx.getUpdateManager()
    updateManager.onCheckForUpdate(function (res) {
      // 请求完新版本信息的回调
      console.log(res.hasUpdate, "版本信息")
    })
    updateManager.onUpdateReady(function () {
      wx.showModal({
        title: "更新提示",
        content: "新版本已经准备好，是否重启应用？",
        success: function (res) {
          if (res.confirm) {
            // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
            updateManager.applyUpdate()
          }
        },
      })
    })
    updateManager.onUpdateFailed(function () {
      // 新的版本下载失败
      wx.showModal({
        title: "更新提示",
        content: "新版本下载失败",
        showCancel: false,
      })
    })
  },
  // 判断是否登录
  getIsLogin() {
    return !!this.globalData.userInfo?.token
  },
  // 初始化配置信息
  async initCommonConfiguration(provinceList, optionsQuery) {
    const { campus: cacheCampus, province: cacheProvince } =
      BASE_CACHE.getBaseCache() || {}
    let { province: queryProvinceKey, campus_id: queryCampusId } = optionsQuery
    queryCampusId = Number(queryCampusId)

    const newCache = {}

    // 设置默认值
    const currentProvinceKey = queryProvinceKey || cacheProvince?.key
    const currentCampusId = queryCampusId || cacheCampus?.id

    // 新用户首次进入
    if (!currentProvinceKey && !currentCampusId) {
      return false
    }

    const campusList = this.getProvinceAllCampus(provinceList)
    // 优先处理校区信息(有传入校区，或有本地校区但是没有传入省份信息)
    if (queryCampusId || (!queryProvinceKey && cacheCampus?.id)) {
      newCache.campus =
        campusList[currentCampusId] ||
        provinceList[queryProvinceKey]?.primary_campus_info ||
        campusList[cacheCampus?.id] ||
        provinceList["chongqing"]?.primary_campus_info
      newCache.province = provinceList[newCache.campus.province]

      try {
        if (!campusList[currentCampusId]) {
          const eventChangeParams = {
            optionsQuery,
            currentCampusId,
            cacheCampusId: cacheCampus?.id,
            cacheProvinceKey: cacheProvince?.key,
            launchOptions: this.globalData.launchOptions,
            provinceList: Object.keys(campusList),
          }
          wx.reportEvent("event_campus_not_found", {
            options: JSON.stringify(eventChangeParams),
          })
        }
      } catch (error) {
        wx.reportEvent("event_campus_not_found", {
          options: error,
        })
      }
    }
    // 处理省份信息
    else if (currentProvinceKey) {
      newCache.province =
        provinceList[currentProvinceKey] || provinceList["chongqing"]

      // 如果当前省份没有设置校区，默认切换到重庆
      if (!newCache.province.primary_campus_info) {
        newCache.province = provinceList["chongqing"]
      }

      newCache.campus = newCache.province.primary_campus_info
    } else {
      // 默认使用重庆校区信息
      newCache.campus = provinceList["chongqing"]?.primary_campus_info
      newCache.province = provinceList[newCache.campus.province]
    }

    this.globalData.hasSetupCampus = true
    // 检查是否需要更新缓存
    if (
      newCache.province?.province_key !== cacheProvince?.key ||
      parseInt(newCache.campus?.id) !== parseInt(cacheCampus?.id)
    ) {
      try {
        // 上报修改操作
        const eventChangeParams = {
          optionsQuery,
          newCacheProvince: newCache?.province?.province_key || "",
          newCacheCampus: newCache?.campus?.id || "",
          oldCacheCampusId: cacheCampus?.id || "",
          oldCcacheProvinceKey: cacheProvince?.key || "",
          launchOptions: this.globalData.launchOptions,
        }
        wx.reportEvent("event_campus_change", {
          options: JSON.stringify(eventChangeParams),
        })
      } catch (error) {}

      BASE_CACHE.setBaseProvinceCache({
        name: newCache.province.province_name,
        key: newCache.province.province_key,
      })
      BASE_CACHE.setBaseCampusCache(newCache.campus)

      try {
        BASE_CACHE.setBaseExamDirectionCache(
          newCache.campus.exam_direction_list[0]
        )
      } catch (error) {
        BASE_CACHE.setBaseExamDirectionCache({})
      }
      return await this.initExamDirection(optionsQuery)
    }

    return false
  },

  // 初始化选择的考试方向
  async initExamDirection(launchOptionsQuery) {
    const { exam_direction: optionsExamDirection } = launchOptionsQuery
    const { examDirection: cacheExamDirection } = BASE_CACHE.getBaseCache()

    // 获取考试方向列表
    const examDirectionRes = await UTIL.request(API.getExamDirectionList)
    const examDirectionList = examDirectionRes.data

    // 查找匹配的考试方向
    const newExamDirection = examDirectionList.find(
      (item) =>
        item.key === optionsExamDirection || item.key === cacheExamDirection.key
    )

    // 更新缓存
    BASE_CACHE.setBaseExamDirectionCache(
      newExamDirection
        ? { name: newExamDirection.name, key: newExamDirection.key }
        : {}
    )

    return true
  },
  // 获取所有校区列表
  getProvinceAllCampus(provinceList) {
    const result = {}
    Object.values(provinceList)?.forEach((item) => {
      item.campus_list?.forEach((citem) => {
        result[citem.id] = citem
      })
    })
    return result
  },

  async getRegionTree() {
    const res = await UTIL.request(API.getRegionTree)
    if (res) {
      console.log(this.globalData.serverConfig, "-----")
      this.globalData.serverConfig.regionData = res.data || []
    }
  },

  // 获取公共配置数据
  getConfiguration: async function () {
    const respose = await UTIL.request(
      API.getCommonConfiguration,
      { launchOptions: this.globalData.launchOptions },
      "get"
    )
    if (respose.data) {
      this.globalData.serverConfig = respose.data
    }
    await this.getRegionTree()
    return respose
  },

  // 获取用户信息
  getUserInfo: async function () {
    let codeRes
    try {
      codeRes = await this.getWxCode()
    } catch (error) {
      codeRes = {}
    }

    const respose = await UTIL.request(API.getUserInfo, {
      code: codeRes.code,
    })
    // 有用户信息时，更新用户信息
    if (respose.error.code === 0) {
      this.setUserInfo(respose.data)
    } else {
      this.setUserInfo({})
    }

    return this.globalData.userInfo
  }, // 检查全局请求是否完成

  async getResume() {
    const res = await UTIL.request(API.getResumeInfo)
    if (res && res.error?.code === 0) {
      this.globalData.resumeInfo = res.data
    }

    return this.globalData.resumeInfo
  },

  /**
   * 判断简历信息是否完善
   * @param {Object} resumeInfo - 简历信息对象
   * @returns {boolean} 如果有任何一项信息已填写则返回true，否则返回false
   */
  isImprove(resumeInfo) {
    // 如果没有传入resumeInfo，返回false
    if (!resumeInfo || typeof resumeInfo !== 'object') {
      return false
    }

    // 遍历resumeInfo的所有属性（排除enter_exam_record）
    for (const [key, value] of Object.entries(resumeInfo)) {
      // 跳过enter_exam_record字段
      if (key === 'enter_exam_record') {
        continue
      }

      // 判断当前字段是否有有效值
      if (this.hasValidValue(value)) {
        console.log(`简历字段 ${key} 已完善:`, value)
        return true
      }
    }

    console.log('简历信息未完善')
    return false
  },

  /**
   * 检查值是否有效（辅助方法）
   * @param {any} value - 要检查的值
   * @returns {boolean} 如果值有效则返回true
   */
  hasValidValue(value) {
    // 如果是null或undefined，返回false
    if (value === null || value === undefined) {
      return false
    }

    // 如果是数组，检查长度
    if (Array.isArray(value)) {
      return value.length > 0
    }

    // 如果是对象，检查text字段
    if (typeof value === 'object') {
      // 检查是否有text字段且不为空
      if (value.hasOwnProperty('text')) {
        return value.text !== null && value.text !== undefined && value.text !== ''
      }
      // 如果没有text字段，检查value字段（备用逻辑）
      if (value.hasOwnProperty('value')) {
        return value.value !== null && value.value !== undefined && value.value !== '' && value.value !== '0' && value.value !== '-1'
      }
      return false
    }

    // 如果是字符串，检查是否非空
    if (typeof value === 'string') {
      return value.trim() !== ''
    }

    // 如果是数字，检查是否不为0
    if (typeof value === 'number') {
      return value !== 0
    }

    // 其他类型视为有效值
    return true
  },

  // 检查全局请求是否完成
  checkLoadRequest: async function () {
    // 辅助函数：获取数据并使用缓存（保持不变）
    const fetchWithCache = async (fetchFunction, cacheKey, setData) => {
      if (!promiseCache.has(cacheKey)) {
        const promise = fetchFunction()
          .then(setData)
          .finally(() => promiseCache.delete(cacheKey))
        promiseCache.set(cacheKey, promise)
      }
      return promiseCache.get(cacheKey)
    } // 收集需要发起的请求数组

    const requests = [] // 用户信息请求

    if (!this.globalData.userInfo) {
      // 优化初次加载请求
      this.globalData.userInfo = USER_CACHE.getUserInfoCache() || null
      requests.push(
        fetchWithCache(
          () => this.getUserInfo(),
          "userInfo",
          (data) => this.setUserInfo(data)
        )
      )
    } // 配置信息请求

    if (!this.globalData.serverConfig) {
      requests.push(
        fetchWithCache(
          () => this.getConfiguration(),
          "serverConfig",
          (data) => {
            this.globalData.serverConfig = data.data
            this.initCommonConfiguration(
              this.globalData.serverConfig.province_list,
              this.globalData.launchOptions.query
            )
          }
        )
      )
    } // 并发执行所有请求

    await Promise.all(requests)

    return true
  },

  // 设置用户信息
  setUserInfo(data) {
    this.globalData.userInfo = data
    USER_CACHE.setUserInfoCache(data)
  },
  // 用户登录
  userLogin: async function (data) {
    const codeData = await this.getWxCode()
    const params = {
      encryptedData: data.encryptedData,
      iv: data.iv,
      code: codeData.code,
    }
    const launchOptionsQuery = this.globalData.launchOptions.query || {}
    if (launchOptionsQuery.province) {
      params.channel_province = launchOptionsQuery.province
    }
    if (launchOptionsQuery.campus_id) {
      params.channel_campus_id = launchOptionsQuery.campus_id
    }

    if (this.globalData.rcode) {
      params.rcode = this.globalData.rcode
    }

    const respose = await UTIL.request(API.userLogin, params)

    // 有用户信息时，更新用户信息
    if (respose.error.code === 0) {
      this.setUserInfo(respose.data)
      return respose.data
    }
    return null
  },

  // 获取code
  getWxCode() {
    return new Promise(function (resolve, reject) {
      wx.login({
        success: (res) => {
          // // console.log(res);
          resolve(res)
        },
        fail: (e) => {
          reject(e)
        },
      })
    })
  },
  // 打开的客服页面
  openCustomerServicePage(params = null) {
    const data = params || this.getCustomerServiceParams()
    console.log(data)
    // 链接类型
    if (data.type === "link") {
      ROUTER.navigateTo({
        path: "/pages/webview/web/index",
        query: {
          url: data.url,
        },
      })
    } else if (data.type === "cc") {
      ROUTER.navigateTo({
        path: "/pages/webview/customerService/index",
        query: {
          customer_url: encodeURIComponent(data.url),
        },
      })
    } else {
      wx.showToast({
        title: "当前客服类型为：" + data.type,
        icon: "none",
      })
    }
  },

  // 获取客服参数
  getCustomerServiceParams() {
    const campusId = BASE_CACHE.getBaseCampusCache()?.id
    const customerServiceList = this.globalData.serverConfig.customer_list
    return (
      customerServiceList[campusId] ||
      Object.values(customerServiceList)[0] ||
      null
    )
  },
  // 检查用户位置信息授权
  checkUserLocationAuthorization() {
    return new Promise(function (resolve, reject) {
      wx.getBase({
        success: function (res) {
          console.log(res.authSetting)
          if (res.authSetting["scope.userLocation"] === undefined) {
            resolve(true)
          } else {
            if (res.authSetting["scope.userLocation"]) {
              resolve(true)
            } else {
              resolve(false)
            }
          }
        },
        fail: function () {
          resolve(false)
        },
      })
    })
  },

  // 获取省份分享信息
  getProvinceShareInfo(baseCache) {
    const configInfo = this.globalData.serverConfig || {}
    const configProvinceList = configInfo.province_list || {}
    const defaultShareInfo = configInfo.share_config
    const provinceKey = baseCache?.province?.key
    if (!provinceKey) {
      return defaultShareInfo
    }
    if (configProvinceList[provinceKey]?.share_config) {
      return configProvinceList[provinceKey]?.share_config
    }
    return defaultShareInfo
  },

  /**
   * 创建分享参数对象。
   * @param {Object} [param={}] - 一个可选的对象，用于自定义分享内容。它可能包含以下属性：
   * @param {string} [param.title] - 分享标题，默认使用全局配置中的分享文本。
   * @param {string} [param.imageUrl] - 分享图片链接，默认使用全局配置中的分享图片。
   * @param {string} [param.path] - 分享链接的基础路径（仅在分享给好友时需要提供）。
   * @param {Object} [param.query] - 路径参数对象。
   * @returns {Object} 返回一个包含了分享所需的参数对象。
   */
  createShareParams(param = {}) {
    const baseCache = BASE_CACHE.getBaseCache()
    const serverShareConfig = this.getProvinceShareInfo(baseCache)
    const result = {
      title: param.title || serverShareConfig.text || "",
      imageUrl: param.imageUrl || serverShareConfig.img || "",
      query: {},
    }
    // 当不传图片时，默认使用系统图片
    if (param.imageUrl === undefined) {
      param.imageUrl = ""
    }

    if (baseCache.campus?.id) {
      param.query.campus_id = baseCache.campus.id
    }
    if (baseCache.province?.key) {
      param.query.province = baseCache.province.key
    }
    // if (baseCache.examDirection?.key) {
    //   param.query.exam_direction = baseCache.examDirection?.key
    // }

    // 分享携带参数
    if (param.query) {
      result.query = Object.assign(result.query, param.query)
    }

    // 分享路径（仅分享好友时传入）
    if (param.path) {
      const path = param.path
      const query = param.query
      delete param.query
      result.path = ROUTER.createPathWithParams(path, query)
    }

    console.log(result)
    return result
  },

  // 补充设置校区信息
  supplementarySetupCampus(data = {}) {
    // 如果之前设置过，就不设置
    if (this.globalData.hasSetupCampus) {
      return
    }
    // 上报数据
    try {
      wx.reportEvent("event_not_province", {
        options: JSON.stringify(this.globalData.launchOptions),
      })
    } catch (error) {
      wx.reportEvent("event_not_province", {
        options: this.globalData.launchOptions,
      })
    }

    const params = { brand: data.brand }

    if (data.province) {
      params.province = data.province
    }

    // 有省份信息且是小程序时才设置
    if (params.province && params.brand === "jbcmp") {
      this.initCommonConfiguration(
        this.globalData.serverConfig.province_list,
        params
      )
    }
  },

  openFile(url, options) {
    wx.showLoading({
      title: "加载中",
    })

    return previewFile(url, options)
      .then((res) => {
        console.log(res)
        wx.hideLoading()
      })
      .catch((res) => {
        console.log(res)
        wx.hideLoading()

        ROUTER.navigateTo({
          path: "/pages/file-preview/index",
          query: { filePath: url },
        })
      })
  },

  // 返回上一页
  backPage() {
    const pages = getCurrentPages() // 获取当前页面栈
    if (pages.length <= 1) {
      // 如果页面栈中只有一个页面，则返回首页
      ROUTER.reLaunch({
        path: "/pages/home/<USER>/index", // 替换为你的首页路径
      })
    } else {
      // 否则返回上一页
      ROUTER.navigateBack()
    }
  },

  globalData: {
    CONFIG, //本地配置文件
    userInfo: null, // 用户信息
    resumeInfo: null, // 简历信息
    serverConfig: null, // 服务器配置

    rcode: null, // 推荐码

    address: null, // 当前收货地址选中的信息
    hasSetupCampus: false, // 是否设置过校区
    hasResume: false, // 是否关闭过完善信息弹窗
    launchOptions: {}, // 小程序进入时的参数
  },
})
