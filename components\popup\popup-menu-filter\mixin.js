/**
 * popup-menu-filter 弹窗管理 Mixin
 * 提供通用的弹窗显示、隐藏、位置计算、滚动控制等功能
 * 使用方式：在页面的 JS 文件中通过 Object.assign 混入
 */

const PopupMenuFilterMixin = {
  /**
   * 弹窗相关的数据
   */
  popupData: {
    // 弹窗显示状态（合并 showMenuPopup 和 showPopupFilter）
    showPopupFilterMenu: false,
    // 遮罩层位置
    overlayTop: 0,
    // 保存的滚动位置
    savedScrollTop: 0,
    // 页面滚动禁用状态
    pageScrollDisabled: false,

    // 页面容器内联样式
    pageContainerStyle: "",
  },

  /**
   * 弹窗相关的方法
   */
  popupMethods: {
    /**
     * 显示弹窗
     * @param {String} positionSelector 用于计算位置的选择器，默认为 '.main-top'
     * @returns {Promise} 返回Promise，在弹窗显示完成后resolve
     */
    async showPopupMenuFilter(positionSelector = ".main-top", isSelectRegion) {
      console.log("显示弹窗:")

      try {
        // 并行执行计算遮罩层位置和禁用页面滚动
        await Promise.all([
          this.calculateOverlayPosition(positionSelector, isSelectRegion),
          this.disablePageScroll(),
        ])

        this.setData({
          showPopupFilterMenu: true,
        })
      } catch (error) {
        console.error("显示弹窗失败:", error)
        throw error
      }
    },

    /**
     * 隐藏弹窗
     */
    hidePopupMenuFilter() {
      console.log("隐藏弹窗")

      // 恢复页面滚动并还原滚动位置
      this.enablePageScroll()

      this.setData({
        showPopupFilterMenu: false,
        overlayTop: 0,
        savedScrollTop: 0,
      })
    },

    /**
     * 计算遮罩层位置
     * @param {String} selector 用于定位的选择器，默认为 '.main-top'
     * @returns {Promise} 返回Promise，在位置计算完成后resolve
     */
    calculateOverlayPosition(selector = ".main-top", isSelectRegion) {
      return new Promise((resolve, reject) => {
        if (this.data.overlayTop) {
          resolve(this.data.overlayTop)
          return
        }

        const query = wx.createSelectorQuery().in(this)
        query
          .select(selector)
          .boundingClientRect((rect) => {
            if (rect) {
              let overlayTop = rect.bottom
              console.log("遮罩层位置:", overlayTop)
              // if (isSelectRegion) {
              //   overlayTop = overlayTop - 42
              // }
              this.setData({
                overlayTop: overlayTop,
              })
              resolve(overlayTop)
            } else {
              const errorMsg = `未找到选择器 ${selector} 对应的元素`
              console.warn(errorMsg)
              reject(new Error(errorMsg))
            }
          })
          .exec()
      })
    },

    /**
     * 禁用页面滚动并保存当前滚动位置
     * @returns {Promise} 返回Promise，在滚动禁用完成后resolve
     */
    disablePageScroll() {
      return new Promise((resolve, reject) => {
        if (this.data.savedScrollTop) {
          resolve(this.data.savedScrollTop)
          return
        }

        // 获取当前页面滚动位置
        wx.createSelectorQuery()
          .selectViewport()
          .scrollOffset((res) => {
            console.log("保存滚动位置:", res.scrollTop)

            // 保存当前滚动位置
            this.setData({
              savedScrollTop: res.scrollTop,
              pageScrollDisabled: true,
              pageContainerStyle: `top: -${res.scrollTop}px;`,
            })
            resolve(res.scrollTop)
          })
          .exec()
      })
    },

    /**
     * 恢复页面滚动并还原滚动位置
     */
    enablePageScroll() {
      const savedScrollTop = this.data.savedScrollTop

      // 先清除固定定位样式
      this.setData({
        pageScrollDisabled: false,
        pageContainerStyle: "",
      })

      // 如果有保存的滚动位置，则恢复到该位置
      if (savedScrollTop > 0) {
        wx.pageScrollTo({
          scrollTop: savedScrollTop,
          duration: 0, // 不使用动画，立即跳转
        })
      }
    },

    /**
     * 检查页面滚动是否被禁用（用于 onPageScroll 中的判断）
     */
    isPageScrollDisabled() {
      return this.data.pageScrollDisabled
    },
  },

  /**
   * 初始化 Mixin
   * 在页面的 onLoad 或组件的 attached 中调用
   */
  initPopupMenuFilterMixin() {
    // 将弹窗数据合并到页面数据中
    const currentData = this.data || {}
    this.setData({
      ...currentData,
      ...this.popupData,
    })

    // 将弹窗方法合并到页面方法中
    Object.assign(this, PopupMenuFilterMixin.popupMethods)

    console.log("PopupMenuFilterMixin 初始化完成")
  },
}

module.exports = PopupMenuFilterMixin
