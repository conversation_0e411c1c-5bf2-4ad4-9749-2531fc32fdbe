// pages/select/select-job/index.js
const ROUTER = require("@/services/mpRouter")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const {
  handleSingleSelect,
  handleMultiSelect,
} = require("@/services/selectionService")
const {
  setJobDetailSelectForTemplateCache,
  getJobDetailSelectForTemplateCache,
  getJobSelectForTemplateCache,
  setJobSelectForTemplateCache,
} = require("@/utils/cache/filterCache")
const APP = getApp()

Page({
  data: {
    show_white: true,

    // 当前选中的分类
    currentCategory: null,

    // 加载状态
    loading: {
      categories: false,
      options: false,
    },

    // 滚动定位
    scrollIntoView: "",

    // 筛选分类列表 - 二级显示结构（仅用于左侧显示）
    filterCategories: [],

    list: [],

    // 选中状态管理对象 - 类似 business-filter 的实现
    positionSelectForTemplate: {},
    positionSelectList: [],
    type: "",
    article_id: "",
    num: 0,
    showCacelGrouponDialog: false,

    // 保存状态标记
    hasUnsavedChanges: false, // 是否有未保存的修改
    initialSelectionState: {}, // 初始选择状态，用于对比
  },

  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    if (options.type) {
      this.setData({
        type: options.type,
      })
    }
    if (options.article_id) {
      this.setData({
        article_id: options.article_id,
      })
    }
    if (this.data.type == "detail") {
      // 初始化数据
      await this.loadFilterCategories()
      this.getArticleJobNum()
    } else {
      // 初始化数据
      await this.getJobFilterMenu()
    }

    // 从页面参数中恢复已选择的筛选条件

    await this.updatePositionForTemplateFromCache()
    if (this.data.list.length > 0) {
      this.updatePositionSelectList(
        this.data.list,
        this.data.positionSelectForTemplate
      )
    }
  },

  onShow() {},
  onShareAppMessage() {},

  backPage() {
    // 检查是否有未保存的修改
    if (this.hasUnsavedChanges()) {
      this.setData({
        showCacelGrouponDialog: true,
      })
      return
    }

    // 没有修改，直接返回
    this.doNavigateBack()
  },

  /**
   * 检查是否有未保存的修改
   * @returns {boolean} 是否有未保存的修改
   */
  hasUnsavedChanges() {
    const currentState = this.data.positionSelectForTemplate
    const initialState = this.data.initialSelectionState

    // 检查是否有选择了内容
    const hasSelections = this.hasAnySelections(currentState)
    if (!hasSelections) {
      // 没有任何选择，不需要弹出对话框
      return false
    }

    // 比较当前状态和初始状态
    return !this.isSelectionStateEqual(currentState, initialState)
  },

  /**
   * 检查是否有任何选择
   * @param {Object} selectionState 选择状态对象
   * @returns {boolean} 是否有选择
   */
  hasAnySelections(selectionState) {
    for (const key in selectionState) {
      const values = selectionState[key]
      if (Array.isArray(values) && values.length > 0) {
        return true
      }
    }
    return false
  },

  /**
   * 比较两个选择状态是否相等
   * @param {Object} state1 状态1
   * @param {Object} state2 状态2
   * @returns {boolean} 是否相等
   */
  isSelectionStateEqual(state1, state2) {
    const keys1 = Object.keys(state1)
    const keys2 = Object.keys(state2)

    if (keys1.length !== keys2.length) {
      return false
    }

    for (const key of keys1) {
      const values1 = state1[key] || []
      const values2 = state2[key] || []

      if (!this.arraysEqual([...values1].sort(), [...values2].sort())) {
        return false
      }
    }

    return true
  },

  /**
   * 执行返回操作
   */
  doNavigateBack() {
    if (getCurrentPages().length <= 1) {
      wx.reLaunch({
        url: "/pages/home/<USER>/index",
      })
      console.log("回首页")
      return false
    }
    console.log("触发返回")
    wx.navigateBack({
      delta: 1,
    })
  },

  /**
   * 对话框确认离开
   */
  confirmLeave() {
    this.setData({
      showCacelGrouponDialog: false,
    })
    this.doNavigateBack()
  },

  /**
   * 对话框取消离开
   */
  cancelLeave() {
    this.setData({
      showCacelGrouponDialog: false,
    })
  },

  /**
   * 保存并离开
   */
  saveAndLeave() {
    this.setData({
      showCacelGrouponDialog: false,
    })
    // 调用确认筛选方法，但不导航（因为confirmFilters会调用wx.navigateBack）
    this.confirmFilters()
  },
  async loadFilterCategories() {
    try {
      const res = await UTIL.request(API.getJobFilteList)
      if (res && res.error && res.error.code === 0 && res.data) {
        this.setData({
          list: res.data.list,
          isRequest: true,
        })
        // 初始化动态菜单
        if (res.data.list.length > 0) {
          this.initMenu(res.data.list)
        }
      }
    } catch (error) {}
  },
  async getJobFilterMenu() {
    try {
      const res = await UTIL.request(API.getJobFilterMenu)
      if (res && res.error && res.error.code === 0 && res.data) {
        const resData = res.data?.job_filter_menu
        const jobMenuData = {}
        resData.forEach((item) => {
          jobMenuData[item.filter_key] = item
        })
        this.setData({
          jobMenuData,
          list: resData[resData?.length - 1].data,
          isRequest: true,
        })
        // 初始化动态菜单
        if (this.data.list.length > 0) {
          this.initMenu(this.data.list)
        }
      }
    } catch (error) {}
  },
  initMenu(list) {
    const updatedLeftCategories = []
    const positionSelectForTemplate = {}

    // 遍历接口返回的数据结构
    list.forEach((group) => {
      const categoryGroup = {
        id: group.key,
        name: group.title,
        children: [],
      }

      // 遍历每个分组下的筛选项
      group.list.forEach((filterItem) => {
        // 初始化 positionSelectForTemplate 中对应的 key
        positionSelectForTemplate[filterItem.filter_key] = []

        // 直接使用原数据结构
        categoryGroup.children.push({
          id: filterItem.filter_key,
          name: filterItem.title,
          ...filterItem, // 直接使用原有数据结构
        })
      })

      updatedLeftCategories.push(categoryGroup)
    })

    // 默认选中第一个分类
    let firstCategory = null
    if (
      updatedLeftCategories.length > 0 &&
      updatedLeftCategories[0].children.length > 0
    ) {
      firstCategory = updatedLeftCategories[0].children[0]
      firstCategory.selected = true
    }

    this.setData({
      filterCategories: updatedLeftCategories,
      currentCategory: firstCategory,
      positionSelectForTemplate: positionSelectForTemplate,
    })
  },
  async getArticleJobNum() {
    const apiParams = {
      ...this.buildApiParams(this.data.positionSelectForTemplate),
      article_id: this.data.article_id,
    }
    try {
      const res = await UTIL.request(API.getArticleJobNum, apiParams)
      if (res && res.error && res.error.code === 0 && res.data) {
        console.log(res)
        this.setData({
          num: res.data.num,
        })
        // this.setData({
        //   list: res.data.list,
        //   isRequest: true,
        // })
        // // 初始化动态菜单
        // if (res.data.list.length > 0) {
        //   this.initMenu(res.data.list)
        // }
      }
    } catch (error) {}
  },
  // 处理接口请求参数
  buildApiParams(selectedData) {
    console.log(selectedData, "拿到的")
    const apiParams = {}

    Object.keys(selectedData).forEach((keyName) => {
      const data =
        selectedData[keyName] || (keyName !== "filter_list" ? [] : {})
      apiParams[keyName] = data
    })
    console.log("得到的参数", apiParams)
    return UTIL.convertArraysToString(apiParams)
  },

  /**
   * 选择分类 - 滚动到对应位置
   */
  selectCategory(e) {
    const { category } = e.currentTarget.dataset

    this.setData({
      currentCategory: category,
      scrollIntoView: `category-${category.id}`,
    })
  },

  /**
   * 从缓存恢复选中状态
   */
  updatePositionForTemplateFromCache() {
    const positionSelectForTemplate = this.data.positionSelectForTemplate
    const { type } = this.data

    let cachePositionSelectForTemplate = {}

    // 根据 type 从不同缓存获取 filter_list
    if (type === "detail") {
      const jobDetailCache = getJobDetailSelectForTemplateCache()
      cachePositionSelectForTemplate = jobDetailCache.filter_list || {}
    } else if (type === "list") {
      const noticeCache = getJobSelectForTemplateCache()
      cachePositionSelectForTemplate = noticeCache.filter_list || {}
    }

    for (const key in positionSelectForTemplate) {
      if (cachePositionSelectForTemplate[key]) {
        positionSelectForTemplate[key] = cachePositionSelectForTemplate[key]
      }
    }
    this.setData({
      positionSelectForTemplate,
    })

    // 保存初始状态，用于检查是否有未保存的修改
    this.setData({
      initialSelectionState: JSON.parse(
        JSON.stringify(positionSelectForTemplate)
      ),
    })
  },
  /**
   * 处理选项点击事件 - 类似 group-list 组件的实现
   */
  handleOptionClick(e) {
    const { value, key } = e.currentTarget.dataset
    const { positionSelectForTemplate, positionSelectList } = this.data

    // 找到对应的筛选项配置
    const filterItem = this.findFilterItemByKey(this.data.list, key)
    if (!filterItem) {
      console.error("未找到筛选项配置:", key)
      return
    }

    const isMultipleChoice = filterItem.is_radio === 0
    const currentSelected = positionSelectForTemplate[key] || []

    // 根据选择类型调用对应的处理方法
    const updatedSelected = isMultipleChoice
      ? handleMultiSelect(currentSelected, value)
      : handleSingleSelect(currentSelected, value, true) // 启用特殊逻辑

    // 更新选中状态
    this.setData({
      [`positionSelectForTemplate.${key}`]: updatedSelected,
    })
    console.log(updatedSelected, "---------22222")
    if (this.data.type === "detail") {
      this.getArticleJobNum()
    }

    console.log(
      this.data.positionSelectForTemplate,
      "--------------------------------"
    )

    // 更新已选择列表
    this.updatePositionSelectList(
      this.data.list,
      this.data.positionSelectForTemplate
    )
  },

  /**
   * 移除已选择的筛选条件
   */
  removeSelectedFilter(e) {
    const { index } = e.currentTarget.dataset
    const { positionSelectList, positionSelectForTemplate } = this.data

    const removedFilter = positionSelectList[index]
    const { filter_key, value } = removedFilter

    // 从 positionSelectForTemplate 中移除该选项
    const currentSelected = positionSelectForTemplate[filter_key] || []
    const updatedSelected = currentSelected.filter(
      (selectedValue) => selectedValue != value
    )

    this.setData({
      [`positionSelectForTemplate.${filter_key}`]: updatedSelected,
    })

    // 更新已选择列表
    this.updatePositionSelectList(
      this.data.list,
      this.data.positionSelectForTemplate
    )
  },

  /**
   * 根据 filter_key 查找筛选项配置
   */
  findFilterItemByKey(list, filterKey) {
    for (let group of list) {
      for (let item of group.list) {
        if (item.filter_key === filterKey) {
          return item
        }
      }
    }
    return null
  },

  /**
   * 更新已选择列表
   */
  updatePositionSelectList(list, positionSelectForTemplate) {
    const selectList = []

    // 遍历所有筛选类型
    for (const filterKey in positionSelectForTemplate) {
      const selectedValues = positionSelectForTemplate[filterKey] || []

      if (selectedValues.length > 0) {
        // 找到对应的筛选项配置
        const filterItem = this.findFilterItemByKey(list, filterKey)
        if (!filterItem) continue

        // 遍历选中的值，直接使用原有字段
        selectedValues.forEach((value) => {
          const option = filterItem.list.find((item) => item.value == value)
          if (option) {
            selectList.push({
              ...option,
              filter_key: filterKey, // 只添加这个字段用于删除时识别类型
            })
          }
        })
      }
    }

    this.setData({
      positionSelectList: selectList,
    })
    console.log(
      this.data.positionSelectList,
      "--------------------------------"
    )
  },

  /**
   * 清空所有选择
   */
  clearAllSelected() {
    const clearedTemplate = {}
    for (const key in this.data.positionSelectForTemplate) {
      clearedTemplate[key] = []
    }

    this.setData({
      positionSelectForTemplate: clearedTemplate,
      positionSelectList: [],
    })
  },
  /**
   * 确认筛选条件
   */
  confirmFilters() {
    const { positionSelectForTemplate, type } = this.data

    // 根据 type 决定存储到哪个缓存
    if (type === "detail") {
      // 获取现有的 jobDetailSelectForTemplate 缓存
      const jobDetailCache = getJobDetailSelectForTemplateCache()
      // 将选中状态存储到 filter_list 字段
      jobDetailCache.filter_list = positionSelectForTemplate
      setJobDetailSelectForTemplateCache(jobDetailCache)
    } else if (type === "list") {
      // 获取现有的 noticeSelectForTemplate 缓存
      const noticeCache = getJobSelectForTemplateCache()
      // 将选中状态存储到 filter_list 字段
      noticeCache.filter_list = positionSelectForTemplate
      setJobSelectForTemplateCache(noticeCache)

      // 对于非detail类型，检查适合我状态
      this.checkAndUpdateFitMeStatus()
    }

    // 重置初始状态，标记修改已保存
    this.setData({
      initialSelectionState: JSON.parse(
        JSON.stringify(positionSelectForTemplate)
      ),
    })

    wx.navigateBack()
  },
  /**
   * 恢复已选择筛选条件
   */
  restoreSelectedFilters(selectedFilters) {
    // 这里可以根据传入的筛选条件恢复状态
    // 具体实现取决于 selectedFilters 的数据格式
    console.log("恢复筛选条件:", selectedFilters)
  },

  /**
   * 检查并更新"适合我"状态
   * 当用户手动修改筛选条件时，检查是否还符合"适合我"的匹配标准
   */
  checkAndUpdateFitMeStatus() {
    // 获取当前的jobSelectForTemplate缓存
    const jobSelectForTemplate = getJobSelectForTemplateCache()

    // 检查当前是否启用了"适合我"
    const fitMeSelected = jobSelectForTemplate?.fit_me || []
    const isFitMeEnabled = fitMeSelected.includes(1)

    if (!isFitMeEnabled) {
      // 如果没有启用"适合我"，无需检查
      return
    }

    console.log("检查适合我匹配状态...")

    // 从不同地方获取matchFilter，这里需要从职位列表页面的菜单数据中获取
    // 因为当前页面可能没有直接的matchFilter数据，我们需要从缓存中获取
    const matchFilter = this.getMatchFilterFromCache()

    if (!matchFilter) {
      console.warn("未找到适合我的匹配条件，无法检查")
      return
    }

    console.log("适合我匹配条件:", matchFilter)
    console.log("当前筛选条件:", this.data.positionSelectForTemplate)

    // 检查每个matchFilter中的条件是否与当前选择完全匹配
    const isStillMatching = this.checkFilterMatching(
      matchFilter,
      this.data.positionSelectForTemplate
    )

    if (!isStillMatching) {
      console.log("当前筛选条件不再匹配适合我标准，自动取消适合我")

      // 取消"适合我"状态
      jobSelectForTemplate.fit_me = []

      // 更新缓存
      setJobSelectForTemplateCache(jobSelectForTemplate)

      // 可以选择显示提示
      wx.showToast({
        title: '筛选条件已变更，已自动取消"适合我"',
        icon: "none",
        duration: 2000,
      })
    }
  },

  /**
   * 从jobMenuData获取matchFilter
   * @returns {Object|null} matchFilter对象
   */
  getMatchFilterFromCache() {
    // 从当前页面的jobMenuData中获取
    if (this.data.jobMenuData?.fit_me?.match_filter) {
      return this.data.jobMenuData.fit_me.match_filter
    }

    console.warn("未找到适合我的匹配条件")
    return null
  },

  /**
   * 检查筛选条件是否匹配
   * @param {Object} matchFilter 适合我的匹配条件
   * @param {Object} currentSelection 当前用户选择的筛选条件
   * @returns {boolean} 是否完全匹配
   */
  checkFilterMatching(matchFilter, currentSelection) {
    // 遍历matchFilter中的每个条件
    for (const filterKey of Object.keys(matchFilter)) {
      const expectedValues = matchFilter[filterKey]
      const currentValues = currentSelection[filterKey] || []

      // 将字符串转换为数组
      const expectedArray =
        typeof expectedValues === "string"
          ? expectedValues.split(",").map((v) => parseInt(v.trim()))
          : expectedValues

      console.log(`检查筛选项 ${filterKey}:`)
      console.log("  期望值:", expectedArray)
      console.log("  当前值:", currentValues)

      // 检查是否完全匹配（数组长度和内容都要一致）
      if (!this.arraysEqual(expectedArray.sort(), currentValues.sort())) {
        console.log(`  筛选项 ${filterKey} 不匹配`)
        return false
      }

      console.log(`  筛选项 ${filterKey} 匹配`)
    }

    console.log("所有筛选条件都匹配")
    return true
  },

  /**
   * 比较两个数组是否相等
   * @param {Array} arr1 数组1
   * @param {Array} arr2 数组2
   * @returns {boolean} 是否相等
   */
  arraysEqual(arr1, arr2) {
    if (arr1.length !== arr2.length) {
      return false
    }

    for (let i = 0; i < arr1.length; i++) {
      if (arr1[i] !== arr2[i]) {
        return false
      }
    }

    return true
  },

  onPageScroll(e) {
    if (e.scrollTop > 0) {
      if (!this.data.show_white) {
        this.setData({ show_white: true })
      }
    } else {
      if (this.data.show_white) {
        this.setData({ show_white: false })
      }
    }
  },
})
