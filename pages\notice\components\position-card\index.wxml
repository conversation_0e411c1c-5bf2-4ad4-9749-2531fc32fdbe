<view class="position-list" wx:if="{{list.length>0}}">
  <view class="position-list-item" wx:for="{{list}}" wx:key="index" data-item="{{item}}" catch:tap="toJobDetail">
    <image class="label-img" wx:if="{{item.follows_tag}}" src="{{item.follows_tag}}"></image>
    <view class="title">{{item.job_name}}</view>
    <block wx:if="{{item.apply_status}}">
      <view class="label-item">
        <text class="status" style="color: {{item.apply_status.color}};">{{item.apply_status.text}}</text><text class="num">共招{{item.need_num}}人</text>
      </view>
      <view class="label-item" wx:if="{{item.work_unit}}">
        招录单位<text class="num text-ellipsis-1">{{item.work_unit}}</text>
      </view>
    </block>
    <block wx:elif="{{!item.apply_status&&item.need_num>0}}">
      <view class="label-item" wx:if="{{item.need_num}}">
        招录人数<text class="num text-ellipsis-1">共招{{item.need_num}}人</text>
      </view>
      <view class="label-item" wx:if="{{item.work_unit}}">
        招录单位<text class="num text-ellipsis-1">{{item.work_unit}}</text>
      </view>
    </block>
    <block wx:else>
      <view class="label-item" wx:if="{{item.work_unit}}">
        招录单位<text class="num text-ellipsis-1">{{item.work_unit}}</text>
      </view>
    </block>
  </view>
</view>