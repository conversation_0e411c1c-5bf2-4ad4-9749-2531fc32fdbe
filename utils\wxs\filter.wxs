/**
 * 筛选选项相关的 WXS 函数
 * 数据结构：{ filter_key: Array<value> } 或 { filter_key: { nested_key: Array<value> } }
 */

/**
 * 判断筛选选项是否被选中
 * @param {string} filterKey 菜单的filter_key
 * @param {string} optionValue 选项的value
 * @param {Object} selectObject 选中状态对象 (noticeSelectForTemplate)
 * @returns {boolean} 是否选中
 */
function isFilterOptionSelected(filterKey, optionValue, selectObject) {
  if (!selectObject || !selectObject[filterKey]) {
    return false
  }

  var selectedValues = selectObject[filterKey]

  // WXS 中处理数组格式的数据
  if (selectedValues && selectedValues.length !== undefined) {
    for (var i = 0; i < selectedValues.length; i++) {
      if (selectedValues[i] == optionValue) {
        return true
      }
    }
  }

  return false
}

function isFilterItemOptionSelected(optionValue, selectedValues) {
  // WXS 中处理数组格式的数据
  if (selectedValues && selectedValues.length !== undefined) {
    for (var i = 0; i < selectedValues.length; i++) {
      if (selectedValues[i] == optionValue) {
        return true
      }
    }
  }

  return false
}

function isSelectOptionSelected(optionValue, selectedValues) {
  // WXS 中处理数组格式的数据
  if (selectedValues && selectedValues.length !== undefined) {
    for (var i = 0; i < selectedValues.length; i++) {
      if (selectedValues[i].id == optionValue) {
        return true
      }
    }
  }

  return false
}

/**
 * 判断菜单项是否被选中（基于 noticeSelectForTemplate）
 * @param {string} filterKey 菜单的filter_key
 * @param {Object} selectObject 选中状态对象 (noticeSelectForTemplate)
 * @returns {boolean} 是否选中
 */
function isMenuItemSelected(filterKey, selectObject) {
  if (!selectObject || !filterKey) {
    return false
  }

  // apply_time 特殊处理：总是返回 false
  if (filterKey === "apply_time") {
    return false
  }

  // 地区类型特殊处理
  if (filterKey === "apply_region") {
    return (
      selectObject.apply_region &&
      selectObject.apply_region.length > 0 &&
      selectObject.apply_region != "0-0-0"
    )
  }

  // 考试动态地区类型特殊处理
  if (filterKey === "region") {
    return selectObject.region && selectObject.region.length > 0
  }

  // 考试类型特殊处理
  if (filterKey === "exam_type") {
    return selectObject.exam_type && selectObject.exam_type.length > 0
  }

  // filter_list 特殊处理：动态遍历所有字段
  // if (filterKey === "filter_list") {
  //   if (!selectObject || !selectObject.filter_list) {
  //     return false
  //   }

  //   var filterList = selectObject.filter_list

  //   var keys = objectKeys(filterList)
  //   console.log("进来没的的的的的的", keys, "23333333333333333333333")
  //   for (var i = 0; i < keys.length; i++) {
  //     var key = keys[i]
  //     var value = filterList[key]
  //     console.log(value, "---------------------------------")
  //     if (value && value.length > 0) {
  //       return true
  //     }
  //   }

  //   return false
  // }
  // filter_list 特殊处理：检查已知的筛选字段
  if (filterKey === "filter_list") {
    if (!selectObject || !selectObject.filter_list) {
      return false
    }

    var filterList = selectObject.filter_list

    // 直接检查已知的筛选字段
    if (
      (filterList.education && filterList.education.length > 0) ||
      (filterList.major && filterList.major.length > 0) ||
      (filterList.politics_face && filterList.politics_face.length > 0) ||
      (filterList.fresh_graduate && filterList.fresh_graduate.length > 0) ||
      (filterList.need_num && filterList.need_num.length > 0) ||
      (filterList.apply_status && filterList.apply_status.length > 0)
    ) {
      return true
    }

    return false
  }

  if (filterKey === "tmp_major") {
    return selectObject.tmp_major &&
      selectObject.tmp_major.selectedMajorIds &&
      selectObject.tmp_major.selectedMajorIds.length
      ? true
      : false
  }
  // 通用处理：检查数组类型字段是否有值
  return hasValidArrayValue(selectObject[filterKey])
}

function objectKeys(obj) {
  var str = JSON.stringify(obj)
  var reg = getRegExp('"(\\w+)":', "g")
  var keys = []
  var result = null

  while ((result = reg.exec(str)) !== null) {
    if (result[1]) {
      keys.push(result[1])
    }
  }

  return keys
}

/**
 * 辅助函数：检查值是否为有效的非空数组
 * @param {*} value 要检查的值
 * @returns {boolean} 是否为有效的非空数组
 */
function hasValidArrayValue(value) {
  // 如果值有 length 属性且 length 大于 0，认为是有效数组
  if (value && value.length !== undefined && value.length > 0) {
    return true
  }

  // 如果值有 length 属性，但为 0 或 undefined，则返回 false
  return false
}

var parseRegion = function (str) {
  if (typeof str !== "string" || str === "") {
    return []
  }

  var arr = str.split("-")
  var result = []

  for (var i = 0; i < arr.length; i++) {
    var num = parseInt(arr[i], 10)
    if (!isNaN(num)) {
      result.push(num)
    }
  }

  return result
}

var includesKey = function (arr, key) {
  if (arr.length == 0) {
    return false
  }

  for (var i = 0; i < arr.length; i++) {
    if (arr[i].key === key) {
      return true
    }
  }

  return false
}

var countMatchingByLevel = function (list, key, level) {
  var targetId = parseInt(key, 10)
  if (isNaN(targetId)) {
    return 0
  }

  var count = 0

  for (var i = 0; i < list.length; i++) {
    var item = list[i]
    if (!item || !item.key) continue

    var parts = item.key.split("-")
    var itemId = parseInt(parts[level - 1], 10)

    if (!isNaN(itemId) && itemId === targetId) {
      count++
    }
  }
  if (count > 0) {
    return count
  }
}

module.exports = {
  isFilterOptionSelected: isFilterOptionSelected,
  isMenuItemSelected: isMenuItemSelected,
  isFilterItemOptionSelected: isFilterItemOptionSelected,
  parseRegion: parseRegion,
  includesKey: includesKey,
  countMatchingByLevel: countMatchingByLevel,
  isSelectOptionSelected: isSelectOptionSelected,
}
