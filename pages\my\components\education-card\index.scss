.education-card {
  padding: 32rpx;
  background: #ffffff;
  border-radius: 16rpx;
  margin-top: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  .title-area {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32rpx;
    .left {
      display: flex;
      align-items: center;
      .title {
        font-size: 30rpx;
        color: #3c3d42;
        font-weight: bold;
        margin-right: 16rpx;
      }
      .tips {
        padding: 4rpx 12rpx;
        box-sizing: border-box;
        background: rgba(255, 106, 77, 0.05);
        border-radius: 8rpx;
        font-size: 20rpx;
        color: #ff6a4d;
      }
    }
    .right {
      display: flex;
      align-items: center;
      .add-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 8rpx;
      }
      .add-text {
        color: #3c3d42;
        font-size: 26rpx;
      }
    }
  }
  .form-list-item {
    width: calc(100% + 32rpx);
    background: #f7f8fa;
    border-radius: 16rpx;
    padding: 24rpx 16rpx;
    box-sizing: border-box;

    &.scroll-item {
      width: calc(100% - 80rpx);
      border-radius: 12rpx;
      margin-right: 16rpx;
      flex-shrink: 0; // 防止被压缩
    }
  }
  // scroll-view容器样式
  .scroll-container {
    width: calc(100% + 48rpx);
    margin-right: -16rpx;
    padding-right: 16rpx;

    .education-scroll {
      width: 100%;
      white-space: nowrap;

      .scroll-content {
        display: flex;
      }
    }
  }

  // 自定义指示器样式
  .custom-indicators {
    display: flex;
    justify-content: center;
    margin-top: 16rpx;

    .indicator-dot {
      width: 8rpx;
      height: 8rpx;
      border-radius: 50%;
      background: #e8e8e8;
      margin: 0 8rpx;
      transition: all 0.3s;

      &.active {
        background: #8f97a8;
      }
    }
  }
  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;
    padding-bottom: 16rpx;
    border-bottom: 1rpx solid #e8e8e8;
    transform: rotateZ(360deg);

    .item-index {
      font-size: 24rpx;
      color: #999999;
    }

    .item-actions {
      display: flex;
      align-items: center;

      .delete-icon {
        width: 32rpx;
        height: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        color: #999999;
        cursor: pointer;
        border-radius: 50%;
        transition: all 0.3s;

        &:active {
          background: rgba(255, 71, 87, 0.1);
          color: #ff4757;
        }
      }
    }
  }
  .form-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 48rpx;

    .left {
      font-size: 26rpx;
      color: #666666;
      min-width: 100rpx;
      display: flex;
      .cred {
        margin-left: 8rpx;
        margin-top: 2rpx;
        color: #e60003;
      }
    }

    /* 选择器样式 */
    .top-area {
      .van-picker__confirm {
        color: #e60003 !important;
      }
    }

    .column-area {
      .van-picker-column__item {
        &.van-picker-column__item--selected {
          color: #3c3d42;
        }
      }
    }

    .active-item {
      color: #3c3d42;
    }
    .right {
      display: flex;
      align-items: center;
      flex: 1;
      justify-content: flex-end;
      min-width: 0; // 允许内容收缩

      .text {
        color: #c2c5cc;
        font-size: 26rpx;
        margin-right: 4rpx;
        max-width: 410rpx; // 新增，与添加页一致

        &.has-value {
          color: #3c3d42;
          font-size: 26rpx;
        }
      }
      .black-text {
        color: #3c3d42 !important;
      }
      .arrow-icon {
        width: 32rpx;
        height: 32rpx;
        flex-shrink: 0; // 防止图标被压缩
      }
    }
  }
  .check-area {
    margin-top: 48rpx;
    padding: 24rpx 8rpx 0 8rpx;
    box-sizing: border-box;
    border-top: 2rpx solid #ebecf0;
    display: flex;
    align-items: center;
    .text {
      color: #666666;
      font-size: 26rpx;
    }
    .img {
      width: 40rpx;
      height: 40rpx;
      margin-right: 8rpx;
    }
    .actvie {
      color: #e60003 !important;
    }
  }
  .mt0 {
    margin-top: 0 !important;
  }
  .mt40 {
    margin-top: 40rpx !important;
  }
  .w100 {
    width: 100%;
  }
}

// scroll-view 内容项的全局样式调整
.education-scroll .scroll-item {
  display: inline-block;
  vertical-align: top;
}

.text-ellipsis-1 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: block;
  max-width: 100%;
}

// 专业字段专用，和添加页保持一致
.major-ellipsis {
  max-width: 410rpx;
  display: inline-block;
  vertical-align: middle;
}

.degree-text {
  font-size: 26rpx;
  color: #3c3d42;
  font-weight: bold;
  .cred {
    color: #e60003;
  }
}
