.nocontent {
  height: 100vh;
  text-align: center;
  padding-top: 300rpx;
  box-sizing: border-box;
  background-color: #fff;
}

.nocontent .icon {
  width: 304rpx;
  height: 304rpx;
}

.nocontent .text1 {
  font-size: 36rpx;
  font-weight: bold;
  color: #3c3d42;
}

.nocontent .text2 {
  font-size: 26rpx;
  line-height: 46rpx;
  color: #919499;
}

.button-box {
  position: fixed;
  bottom: 40rpx;
  left: 50%;
  width: 100%;
  padding-top: 20rpx;
  transform: translateX(-50%);
}

.button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 590rpx;
  height: 90rpx;
  padding: 0;
  margin: 0 auto;
  font-size: 32rpx;
  border-radius: 50rpx;
  background: var(--main-color);
  color: #fff;
}

page {
  background-color: #eeeeee;
}
