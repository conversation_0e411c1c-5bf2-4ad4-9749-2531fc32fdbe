/**
 * 数据选择处理服务
 * 统一处理单选、多选、重置等选择逻辑
 * 用于薪资选择、考试类型选择、职位类型选择等场景
 */

/**
 * 单选方法 - 用于薪资选择场景
 * @param {Array} selectedArray - 当前选中的值数组
 * @param {*} value - 被点击的值
 * @param {boolean} supportSpecialLogic - 是否支持特殊逻辑（-1值特殊处理）
 * @returns {Array} 更新后的选中值数组，支持再次点击取消选中
 */
function handleSingleSelect(selectedArray, value, supportSpecialLogic = false) {
  if (!Array.isArray(selectedArray)) {
    console.warn("handleSingleSelect: selectedArray 必须是数组", { selectedArray })
    selectedArray = []
  }

  // 检查当前值是否已经选中
  const isCurrentlySelected = selectedArray.includes(value)
  
  if (supportSpecialLogic) {
    // 特殊逻辑：处理-1值的特殊情况
    if (value === -1) {
      // 如果点击的是-1，正常切换
      if (isCurrentlySelected) {
        console.log("单选取消选中-1:", value)
        return selectedArray.filter(v => v !== -1)
      } else {
        console.log("单选选中-1:", value)
        return [...selectedArray, -1]
      }
    } else {
      // 如果点击的不是-1
      if (isCurrentlySelected) {
        // 取消选中当前值，但保留-1（如果存在）
        const newArray = selectedArray.filter(v => v !== value)
        console.log("单选取消选中非-1值:", value, "保留-1，结果:", newArray)
        return newArray
      } else {
        // 选中新值，保留-1（如果存在），移除其他非-1值
        const has_minus_one = selectedArray.includes(-1)
        const newArray = has_minus_one ? [-1, value] : [value]
        console.log("单选选中非-1值:", value, "是否保留-1:", has_minus_one, "结果:", newArray)
        return newArray
      }
    }
  } else {
    // 原有逻辑：标准单选
    if (isCurrentlySelected) {
      // 如果已选中，则取消选中（返回空数组）
      console.log("单选取消选中:", value)
      return []
    } else {
      // 如果未选中，则选中该值（单选只能有一个值）
      console.log("单选选中:", value)
      return [value]
    }
  }
}

/**
 * 多选方法 - 用于考试类型、职位类型选择场景
 * @param {Array} selectedArray - 当前选中的值数组
 * @param {*} value - 被点击的值
 * @returns {Array} 更新后的选中值数组，支持多个值同时选中和取消选中
 */
function handleMultiSelect(selectedArray, value) {
  if (!Array.isArray(selectedArray)) {
    console.warn("handleMultiSelect: selectedArray 必须是数组", { selectedArray })
    selectedArray = []
  }

  const index = selectedArray.indexOf(value)
  if (index > -1) {
    // 存在，移除（取消选中）
    const newArray = selectedArray.filter((item) => item !== value)
    console.log("多选取消选中:", value, "当前选中:", newArray)
    return newArray
  } else {
    // 不存在，添加（选中）
    const newArray = [...selectedArray, value]
    console.log("多选选中:", value, "当前选中:", newArray)
    return newArray
  }
}

/**
 * 重置方法 - 用于清空所有选择
 * @param {Array} list - 当前数据列表
 * @returns {Array} 所有项目都未选中状态的数据列表
 */
function handleReset(list) {
  if (!Array.isArray(list)) {
    console.warn("handleReset: 参数无效", { list })
    return []
  }

  // 创建新的数据列表副本，清除所有选中状态
  const resetList = list.map((item) => ({
    ...item,
    selected: false,
  }))

  return resetList
}

/**
 * 获取选中项数组 - 辅助方法
 * @param {Array} list - 数据列表
 * @returns {Array} 选中的项目数组
 */
function getSelectedItems(list) {
  if (!Array.isArray(list)) {
    console.warn("getSelectedItems: 参数无效", { list })
    return []
  }

  return list.filter((item) => item.selected === true)
}

/**
 * 设置选中项 - 辅助方法
 * @param {Array} list - 数据列表
 * @param {Array} selectedValues - 要选中的value数组
 * @returns {Array} 更新后的数据列表
 */
function setSelectedItems(list, selectedValues = []) {
  if (!Array.isArray(list)) {
    console.warn("setSelectedItems: 参数无效", { list, selectedValues })
    return []
  }

  if (!Array.isArray(selectedValues)) {
    console.warn("setSelectedItems: selectedValues必须是数组", {
      selectedValues,
    })
    return list
  }

  // 创建新的数据列表副本
  const updatedList = list.map((item) => ({
    ...item,
    selected: selectedValues.includes(item.value),
  }))

  return updatedList
}

// 导出所有方法
module.exports = {
  handleSingleSelect,
  handleMultiSelect,
  handleReset,
  getSelectedItems,
  setSelectedItems,
}
