const { request } = require("./request")

// 发起网络请求
exports.request = request

// 获取文件名称及后缀
exports.extractFileNameAndExtension = function (filePath) {
  if (!filePath) {
    return ""
  }
  const segments = filePath.split("/")
  const fileNameWithExtension = segments[segments.length - 1]

  const lastDotIndex = fileNameWithExtension.lastIndexOf(".")
  if (lastDotIndex !== -1) {
    const fileName = fileNameWithExtension.slice(0, lastDotIndex)
    const fileExtension = fileNameWithExtension.slice(lastDotIndex + 1)
    const fullName = `${fileName}.${fileExtension}`
    return fullName
  } else {
    return fileNameWithExtension
  }
}

// 将对象的Value 格式化为指定格式
exports.formatObjectValue = function (object, keyNameArray, formatCall) {
  keyNameArray.forEach(function (keyName) {
    object[`new_${keyName}`] = formatCall(object[keyName])
  })
  return object
}

exports.formatSingleNumber = function (number) {
  return number <= 9 ? "0" + number : number
}

// 格式化日期
exports.formatDate = function (time) {
  function formatSingleNumber(number) {
    return number <= 9 ? "0" + number : number
  }
  const date = new Date(time * 1000),
    year = date.getFullYear(),
    month = formatSingleNumber(date.getMonth() + 1),
    day = formatSingleNumber(date.getDate()),
    hour = formatSingleNumber(date.getHours()),
    minute = formatSingleNumber(date.getMinutes()),
    second = formatSingleNumber(date.getSeconds()),
    md = month + "月" + day + "日",
    ms = minute + ":" + second,
    hm = hour + ":" + minute,
    hms = hour + ":" + ms

  return {
    year,
    month,
    day,
    hour,
    minute,
    second,
    md,
    ms,
    hm,
    hms,
  }
}

// 格式化时间戳
exports.formatTimeStamp = function (time) {
  function formatSingleNumber(number) {
    return number <= 9 ? "0" + number : number
  }
  time = time >>> 0
  let hour = parseInt(time / 3600),
    minute = parseInt((time % 3600) / 60),
    second = parseInt(time % 60),
    ms = formatSingleNumber(minute) + ":" + formatSingleNumber(second),
    hms = formatSingleNumber(hour) + ":" + ms
  return {
    hour,
    minute,
    second,
    ms,
    hms,
  }
}

// 获取元素信息
exports.getElementOffset = function (elementName, scope) {
  let query = wx.createSelectorQuery().in(scope || this)
  query.select(elementName).boundingClientRect()
  query.selectViewport().scrollOffset()
  return new Promise(function (res, rej) {
    query.exec(function (ress) {
      res(ress)
    })
  })
}

exports.getElementInfo = function (elementName, scope) {
  const query = wx.createSelectorQuery().in(scope || this)
  return new Promise((resolve, reject) => {
    query
      .select(elementName)
      .boundingClientRect((rect) => {
        resolve(rect)
      })
      .exec()
  })
}

/**
 * 节流函数
 * @param {*} fn 是我们需要包装的事件回调
 * @param {*} wait 是每次推迟执行的等待时间
 */
exports.throttle = function (fn, wait = 3000) {
  let last
  let timer
  return function () {
    let context = this,
      args = arguments
    let now = new Date()

    if (last && now - last < wait) {
      // 如果在指定的时间内，则重置定时器
      clearTimeout(timer)
      timer = setTimeout(function () {
        last = now
        // fn.apply(context, args);
      }, wait)
    } else {
      // 如果超过了指定的时间，则直接执行函数
      last = now
      fn.apply(context, args)
    }
  }
}

/**
 * 防抖函数
 * @param {*} fn 是我们需要包装的事件回调
 * @param {*} wait 是每次推迟执行的等待时间
 */
export function debounce(fn, wait = 3000) {
  let timeout
  return function () {
    const context = this,
      args = arguments
    clearTimeout(timeout)
    timeout = setTimeout(function () {
      fn.apply(context, args)
    }, wait)
  }
}

exports.add = function (num1, num2) {
  return Number(((num1 * 100 + num2 * 100) / 100).toFixed(2))
}

// 定义一个函数来获取当前时间戳，精确到秒
exports.getCurrentTimestampInSeconds = function () {
  return Math.floor(Date.now() / 1000)
}

// 生成url
exports.createUrl = function (basePath, params) {
  let query = Object.keys(params)
    .map((key) => `${key}=${params[key]}`)
    .join("&")

  return `${basePath}?${query}`
}

// 解析url参数
exports.parseUrlQueryParams = function (url) {
  const queryParams = {}
  const queryStringIndex = url.indexOf("?")
  if (queryStringIndex !== -1) {
    const queryString = url.substring(queryStringIndex + 1)
    queryString.split("&").forEach((part) => {
      const equalIndex = part.indexOf("=")
      if (equalIndex !== -1) {
        const key = decodeURIComponent(part.substring(0, equalIndex))
        const value = decodeURIComponent(part.substring(equalIndex + 1))
        queryParams[key] = value
      } else {
        // 处理无等号的情况，例如 "example.com/page?param" 中的 "param"
        queryParams[decodeURIComponent(part)] = null
      }
    })
  }
  return queryParams
}

// 清除价格零
exports.clearPriceZero = (old) => {
  //拷贝一份 返回去掉零的新串
  let newstr = old
  //循环变量 小数部分长度
  var leng = old.length - old.indexOf(".") - 1
  //判断是否有效数
  if (old.indexOf(".") > -1) {
    //循环小数部分
    for (let i = leng; i > 0; i--) {
      //如果newstr末尾有0
      if (
        newstr.lastIndexOf("0") > -1 &&
        newstr.substr(newstr.length - 1, 1) == 0
      ) {
        var k = newstr.lastIndexOf("0")
        //如果小数点后只有一个0 去掉小数点
        if (newstr.charAt(k - 1) == ".") {
          return newstr.substring(0, k - 1)
        } else {
          //否则 去掉一个0
          newstr = newstr.substring(0, k)
        }
      } else {
        //如果末尾没有0
        return newstr
      }
    }
  }
  return old
}

// 提取html中的图片路径
exports.extractHtmlImageUrl = (html) => {
  let rgx_lable = /<img.*?(jpg|png)/g,
    matchList = html.match(rgx_lable) || [],
    arr = []
  arr = matchList.map(function (lable) {
    let urlIndex = lable.indexOf('src="')
    return lable.slice(urlIndex + 5, lable.length)
  })
  return arr
}

// 获取当天日期
exports.getTodayDate = () => {
  const today = new Date()
  const year = today.getFullYear()
  const month = String(today.getMonth() + 1).padStart(2, "0") // 月份从0开始，需要+1
  const day = String(today.getDate()).padStart(2, "0")

  return `${year}-${month}-${day}`
}

/**
 * 将对象中的数组转换为逗号分隔的字符串
 * @param {Object} obj - 需要处理的对象
 * @returns {Object} 处理后的对象，数组字段被转换为字符串
 */
/**
 * 将对象中的数组字段转换为逗号分隔的字符串
 * @param {Object} obj - 需要处理的对象
 * @returns {Object} 返回一个新对象，其中所有数组字段已转换为字符串
 */
exports.convertArraysToString = function(obj) {
  if (!obj || typeof obj !== 'object') {
    return obj
  }
  
  const result = {}
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key]
      
      if (Array.isArray(value)) {
        // 将数组转换为逗号分隔的字符串
        result[key] = value.join(',')
      } else {
        // 非数组字段保持原样
        result[key] = value
      }
    }
  }
  
  return result
}
