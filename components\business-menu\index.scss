/**
 * 业务筛选菜单组件样式
 */

.business-menu-container {
  width: 100%;
  background-color: transparent;

  .menu-list {
    display: flex;
    align-items: center;
    white-space: nowrap;
    overflow-x: auto;

    // 隐藏滚动条
    &::-webkit-scrollbar {
      display: none;
    }

    .menu-list-item {
      font-size: 26rpx;
      color: #666;
      background: #fff;
      align-items: center;
      justify-content: center;
      display: inline-flex;
      height: 58rpx;
      border: 1rpx solid rgba(225, 226, 230, 1);
      border-radius: 12rpx;
      transform: rotateZ(360deg);
      margin-right: 16rpx;
      padding: 0 16rpx 0 24rpx;
      white-space: nowrap;
      flex-shrink: 0;

      // 单选类型不显示箭头，padding调整
      &.no-arrow {
        padding: 0 30rpx;
      }

      // 选中状态
      &.selected {
        color: rgba(230, 0, 3, 1);
        border-color: rgba(230, 0, 3, 1);
        background-color: rgba(230, 0, 3, 0.05);
      }

      // 展开状态
      &.expanded {
        color: rgba(230, 0, 3, 1);
        border-color: rgba(230, 0, 3, 1);
        background-color: rgba(230, 0, 3, 0.05);
      }

      // 菜单项文本
      .menu-text {
        display: flex;
        align-items: center;
        gap: 0;
        font-size: 26rpx;
      }

      // 箭头图标
      .arrow-icon {
        width: 32rpx;
        height: 32rpx;
        margin-left: 0;

        &.zhuan {
          transform: rotate(-180deg);
        }
      }

      // 激活状态
      &:active {
        background-color: rgba(230, 0, 3, 0.1);
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }
}

// 自定义样式类支持
.custom-business-menu {
  // 允许外部自定义样式
}

.rigeion-list {
  padding-top: 24rpx;
  position: relative;
  padding-right: 50rpx;

  .region-list-ul {
    display: flex;
    white-space: nowrap;

    .region-list-item {
      background: rgba(235, 236, 240, 0.6);
      border-radius: 8rpx;
      font-size: 22rpx;
      height: 52rpx;
      padding: 0 32rpx;
      display: inline-flex;
      align-items: center;
      margin-right: 16rpx;

      &.active {
        background: rgba(230, 0, 3, 0.05);
        color: rgba(230, 0, 3, 1);
        font-weight: bold;
      }
    }
  }

  .right-box {
    padding-top: 24rpx;
    position: absolute;
    padding-left: 22rpx;
    background: rgba(247, 248, 250, 1);
    display: flex;
    right: 0;
    top: 0;
    height: 100%;

    .add-img {
      width: 35rpx;
      height: 35rpx;
      margin-top: 8rpx;
    }
  }
}

// // 响应式适配
// @media (max-width: 750rpx) {
//   .business-menu-container {
//     .menu-list {
//       .menu-list-item {
//         padding: 0 12rpx 0 16rpx;
//         font-size: 22rpx;

//         &.no-arrow {
//           padding: 0 24rpx;
//         }
//       }
//     }
//   }
// }

.menu-layout-fixed {
  display: flex;

  .left-menu {
    flex: 1;
    min-width: 0;
  }

  .right-menu {
    &.examShow {
      width: 100%;
      justify-content: space-between;
      .menu-list-item {
        flex: 1;
        position: relative;
        margin-right: 0;
        &::after {
          position: absolute;
          display: block;
          content: " ";
          width: 1rpx;
          height: 32rpx;
          background: #e1e2e6;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
        }
        &:last-child {
          &::after {
            display: none;
          }
        }
      }
    }
    .menu-list-item {
      border-color: transparent;
      background-color: transparent;
      padding: 0;
      margin-right: 32rpx;

      &.selected {
        border-color: transparent;
        background-color: transparent;
      }

      &.expanded {
        border-color: transparent;
        background-color: transparent;
      }
    }
  }
}

.img {
  width: 32rpx;
  height: 32rpx;
}

.fit-me-box {
  height: 58rpx;
  display: inline-flex;
  align-items: center;
  border: 1rpx solid transparent;
  white-space: nowrap;
  flex-shrink: 0;
  margin-right: 32rpx;
  .fit-img {
    width: 120rpx;
    height: 32rpx;
    display: block;
    transform: translateY(8rpx);
  }
}

.text-box {
  display: flex;
  align-items: center;
  .yuan {
    width: 32rpx;
    height: 32rpx;
    margin-right: 4rpx;
  }
  .text {
    height: 26rpx;
    width: 78rpx;
  }
}
