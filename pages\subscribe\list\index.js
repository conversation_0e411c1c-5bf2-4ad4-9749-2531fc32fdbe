const APP = getApp()
const API = require("@/config/api")
const UTIL = require("@/utils/util")
const ROUTER = require("@/services/mpRouter")
Page({
  data: {
    show: false,
    examTypeList: [],
    zZQ: [], // 用来展示已订阅考试范围
    selectObj: {
      exam_type: null, // 改为null，表示未选择
      region_list: [],
    }, //保存考试范围的数据结构
    examPopuSelectForTemplate: {
      apply_region: [], // 地区选择组件的数据
    },
    activeIndex: 0,
    selectedExamTypeId: null, // 当前选中的考试类型ID
    canNext: false, // 是否可以进入下一步
    showExamRegionPopup: true, // 控制地区选择组件的显示
    regionList: [],
    isLogin: false,
    subscribedData: null,
    overlayShow: false,
    isSubscribeData: false, // 是否关注
    isSelectListLength: false,
    isPageResquest: false,
    pageLoading: true, // 页面加载状态
  },

  async onLoad(options) {
    await APP.checkLoadRequest()
    wx.setNavigationBarColor({
      frontColor: "#ffffff",
      backgroundColor: "#000000",
    })

    try {
      await this.getSubscribedConfig()
      await this.getSubscribedData()
      this.setData({
        isLogin: !!APP.globalData.userInfo?.token,
        isSelectListLength: this.data.subscribedData?.list?.length > 0,
      })
    } finally {
      // onLoad完成后显示页面内容
      this.setData({
        pageLoading: false,
      })
    }
  },
  async onShow() {
    this.setData({
      pageLoading: false,
    })
    console.log(this.data.pageLoading, "12312312312")
    if (!this.data.isPageResquest) {
      return
    }

    // 显示loading状态
    this.setData({
      // pageLoading: true,
      isSelectListLength: true,
    })

    try {
      // 重新获取数据
      await this.getSubscribedConfig()
      await this.getSubscribedData()

      // 更新登录状态和其他数据
      this.setData({
        isLogin: !!APP.globalData.userInfo?.token,
        isSelectListLength: this.data.subscribedData?.list?.length > 0,
      })
    } catch (error) {
      console.error("onShow数据加载失败:", error)
    } finally {
      // 隐藏loading状态
      this.setData({
        pageLoading: false,
      })
    }
  },
  onClickHide() {
    this.setData({
      overlayShow: false,
    })
  },
  backPage() {
    if (!this.data.isSelectListLength) {
      this.setData({
        isSelectListLength: true,
      })
    } else {
      if (getCurrentPages().length <= 1) {
        wx.reLaunch({
          url: "/pages/home/<USER>/index",
        })
        console.log("回首页")
        return false
      }
      console.log("触发返回")
      wx.navigateBack({
        delta: 1,
      })
    }
  },
  tapMenuItem() {
    const cmd = this.data.subscribedData.subscribe_icons?.cmd_json
    console.log(cmd)
    APP.toCmdUnitKey(cmd)
  },

  /**
   * 点击新增按钮，打开弹窗
   */
  handleAddSubscribe() {
    this.setData({
      isSelectListLength: false,
      activeIndex: 0,
      selectedExamTypeId: null,
      canNext: false,
      // "selectObj.exam_type": null,
      // "selectObj.region_list": [],
      // "examPopuSelectForTemplate.apply_region": [],
    })
  },

  /**
   * 关闭弹窗
   */
  onClose() {
    this.setData({
      show: false,
    })
  },
  /**
   * 处理考试类型选择
   */
  handleExamTypeSelect(e) {
    const { index } = e.currentTarget.dataset
    const examType = this.data.examTypeList[index]

    // 检查是否已经选择了该考试方向
    const existingItem = this.data.selectList.find(
      (item) => item.exam_type === examType.id
    )

    let selectedRegions = []
    let regionList = []

    if (existingItem) {
      // 如果该考试方向已存在，使用之前的地区数据
      regionList = existingItem.region_list || []
      selectedRegions = existingItem.region_list || []
    }

    this.setData({
      selectedExamTypeId: examType.id,
      "selectObj.exam_type": examType.id,
      "selectObj.region_list": regionList,
      "examPopuSelectForTemplate.apply_region": selectedRegions,
      canNext: true, // 选择后可以进入下一步
    })
  },

  /**
   * 删除订阅的考试范围
   */
  handleDeleteSubscribe(e) {
    const { examType } = e.currentTarget.dataset
    this.delSubscribedData(examType)
    // wx.showModal({
    //   title: "",
    //   content: "确定要删除这个考试范围吗？",
    //   success: (res) => {
    //     if (res.confirm) {
    //       this.delSubscribedData(examType)
    //     }
    //   },
    // })
  },

  /**
   * 下一步按钮点击
   */
  changeNext() {
    // 如果不能进入下一步，直接返回
    if (!this.data.canNext) {
      return
    }

    this.setData({
      activeIndex: 1,
    })
  },

  /**
   * 删除考试范围
   */
  async delSubscribedData(examType) {
    try {
      const res = await UTIL.request(API.delSubscribedData, {
        exam_type: examType,
      })

      if (res && res.error && res.error.code === 0) {
        wx.showToast({
          title: "删除成功",
          icon: "none",
        })

        // 重新获取订阅数据
        this.getSubscribedData()
      } else {
        wx.showToast({
          title: res?.error?.message || "删除失败",
          icon: "none",
        })
      }
    } catch (error) {
      console.error("删除订阅失败:", error)
      wx.showToast({
        title: "网络错误",
        icon: "none",
      })
    }
  },
  /**
   * 处理地区选择完成
   */
  handlePopuRegionSelection(e) {
    const { tempSelected } = e.detail
    console.log("地区选择结果:", tempSelected)

    // 提取region_list（key数组）
    const regionList = tempSelected.map((item) => item.key).filter((key) => key)

    this.setData({
      "selectObj.region_list": regionList,
    })

    // 调用添加订阅数据的方法
    this.addSubscribedData()
  },

  // 编辑考试范围
  editExam(e) {
    const data = e.currentTarget.dataset.item
    console.log(data, "123123123")
    this.setData({
      activeIndex: 0,
      canNext: true, // 选择后可以进入下一步
      isSelectListLength: false,
      selectedExamTypeId: data.exam_type,
      "selectObj.exam_type": data.exam_type,
      "selectObj.region_list": data.region_list,
      "examPopuSelectForTemplate.apply_region": data.region_list,
    })
  },

  /**
   * 添加订阅数据
   */
  async addSubscribedData() {
    const { exam_type, region_list } = this.data.selectObj

    if (!exam_type || !region_list.length) {
      wx.showToast({
        title: "请完善订阅信息",
        icon: "none",
      })
      return
    }

    try {
      const res = await UTIL.request(API.addSubscribedData, {
        exam_type,
        region_list,
      })

      if (res && res.error && res.error.code === 0) {
        if (this.data.subscribedData.is_subscribe == 0) {
          ROUTER.navigateTo({
            path: "/package-goods/subscribe/subcribe-center/index",
            query: {},
          })
          setTimeout(() => {
            // 清空数据并关闭弹窗
            this.setData({
              isSelectListLength: true,
              selectedExamTypeId: null,
              canNext: false,
              "selectObj.exam_type": null,
              "selectObj.region_list": [],
              "examPopuSelectForTemplate.apply_region": [],
            })
          }, 1000)
          return
        }
        this.setData({
          isSelectListLength: true,
          selectedExamTypeId: null,
          canNext: false,
          "selectObj.exam_type": null,
          "selectObj.region_list": [],
          "examPopuSelectForTemplate.apply_region": [],
        })

        // 重新获取订阅数据
        this.getSubscribedData()
      } else {
        wx.showToast({
          title: res?.error?.message || "订阅失败",
          icon: "none",
        })
      }
    } catch (error) {
      console.error("添加订阅失败:", error)
      wx.showToast({
        title: "网络错误",
        icon: "none",
      })
    }
  },
  stepSelection() {
    this.setData({
      activeIndex: 0,
    })
  },
  async getSubscribedData() {
    const res = await UTIL.request(API.getSubscribedData)
    if (res && res.error && res.error.code === 0 && res.data) {
      this.setData({
        selectList: res.data.list,
        subscribedData: res.data,
        isPageResquest: true,
      })
    }
  },
  async getSubscribedConfig() {
    const res = await UTIL.request(API.getSubscribedConfig)
    console.log(res, "222223333")
    if (res && res.error && res.error.code === 0 && res.data) {
      this.setData({
        examTypeList: res.data.examtype_list,
      })
    }
  },
  onReachBottom() {},
})
