<view class="position-list-item" wx:for="{{list}}" wx:key="index" catch:tap="toJobDetail" data-item="{{item}}">
  <image class="label-img" wx:if="{{item.follows_tag}}" src="{{item.follows_tag}}"></image>
  <view class="title-area">
    <view class="title">{{item.job_name}}</view>
  </view>
  <view class="label-item" wx:if="{{item.apply_status}}">
    <text class="status" style="color: {{item.apply_status.color}};">{{item.apply_status.text}}</text><text class="num">共招{{item.need_num}}人</text>
  </view>
  <view class="label-item" wx:if="{{item.work_unit}}">
    招录单位<text class="num">{{item.work_unit}}</text>
  </view>
  <view class="label-item" wx:if="{{item.article_name}}">
    来自公告<text class="num text-ellipsis-1">{{item.article_name}}</text>
  </view>
  <view class="bottom">
    <view class="where" wx:if="{{item.exam_type_name}}">{{item.exam_type_name}}</view>
    <view class="time" wx:if="{{item.release_time}}">{{item.release_time}}</view>
  </view>
</view>