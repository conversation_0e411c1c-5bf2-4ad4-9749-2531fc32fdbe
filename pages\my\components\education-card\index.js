const ROUTER = require("@/services/mpRouter")
const API = require("@/config/api")
const UTIL = require("@/utils/util")
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 教育经历数据
    educationData: {
      type: Array,
      value: [],
    },
    // 是否为应届生
    isGraduate: {
      type: String,
      value: "",
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    educationList: [],
    graduateStatus: "",
    incompleteTips: "",
    currentIndex: 0, // 当前显示的项目索引

    // 应届生身份选择器
    graduateShow: false,
    graduateColumns: [],
    graduateSelectedIndex: 0,
  },

  /**
   * 生命周期
   */
  lifetimes: {
    attached() {
      this.initGraduateSelector()
      // 延迟调用，确保graduateColumns已初始化
      setTimeout(() => {
        this.updateEducationData()
      }, 0)
    },
  },
  pageLifetimes: {
    show() {
      this.initGraduateSelector()
      setTimeout(() => {
        this.updateEducationData()
      }, 0)
      this.triggerEvent("dataChange", {
        educationList: this.data.educationList,
        graduateStatus: this.data.graduateStatus,
      })
    },
  },
  observers: {
    "educationData, isGraduate": function (educationData, isGraduate) {
      // 确保graduateColumns已初始化
      if (!this.data.graduateColumns || !this.data.graduateColumns[0]) {
        this.initGraduateSelector()
      }
      setTimeout(() => {
        // 无论是否有数据都调用 updateEducationData，确保数据同步
        this.updateEducationData()
      }, 0)
    },
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化应届生身份选择器
     */
    initGraduateSelector() {
      const graduateData = [
        { text: "是", value: "1" },
        { text: "否", value: "0" },
      ]

      this.setData({
        graduateColumns: [{ values: graduateData }],
      })
    },
    /**
     * 更新教育经历数据
     */
    updateEducationData() {
      const educationData = this.properties.educationData || []
      const isGraduate = this.properties.isGraduate || ""
      // 如果没有数据，创建一个空的占位数组
      const educationList = educationData.length > 0 ? educationData : []
      // 处理graduateText
      let graduateText = ""
      if (isGraduate) {
        const options =
          (this.data.graduateColumns[0] &&
            this.data.graduateColumns[0].values) ||
          []
        const found = options.find((opt) => opt.value === isGraduate)
        graduateText = found ? found.text : ""
      }
      this.setData({
        educationList,
        graduateStatus: isGraduate,
        graduateText,
        incompleteTips: this.calculateIncompleteTips(educationList, isGraduate),
      })
      this.countIncomplete()
    },

    /**
     * 计算待完善提示
     */
    calculateIncompleteTips(educationList, graduateStatus) {
      let incompleteCount = 0

      // 计算教育经历中的待完善项
      if (educationList.length === 0) {
        incompleteCount += 3 // 学历、专业、学位
      } else {
        educationList.forEach((education) => {
          if (!education.degree) incompleteCount++
          if (!education.major) incompleteCount++
          if (!education.diploma) incompleteCount++
        })
      }

      // 应届生身份
      if (!graduateStatus) {
        incompleteCount++
      }

      return incompleteCount > 0 ? `${incompleteCount}项待完善` : ""
    },

    /**
     * 统计本卡片待完善项数（教育经历卡片整体1项+应届生身份1项）
     */
    countIncomplete() {
      const educationList = this.data.educationList || []
      let count = 0
      // 教育经历卡片整体
      if (!Array.isArray(educationList) || educationList.length < 1) count++
      // 应届生身份graduateStatus
      if (
        !this.data.graduateStatus ||
        String(this.data.graduateStatus).trim().length < 1
      )
        count++
      this.setData({
        incompleteTips: count > 0 ? `${count}项待完善` : "已完善",
      })
      this.triggerEvent("incompleteCount", { count })
    },

    /**
     * 跳转到添加教育经历页面
     */
    onNavigateToAddEducation(e) {
      const { id } = e.currentTarget.dataset
      let query = {
        id,
      }
      if (!id) {
        query = null
      }
      ROUTER.navigateTo({
        path: "/pages/my/addEducation/index",
        query,
      })
    },

    /**
     * 删除教育经历
     */
    onDeleteEducation(e) {
      const { index, id } = e.currentTarget.dataset
      console.log("删除教育经历", { index })

      wx.showModal({
        title: "确认删除",
        content: `确定要删除第${index + 1}条教育经历吗？`,
        success: (res) => {
          if (res.confirm) {
            UTIL.request(API.deleteEducation, {
              id,
            }).then((res) => {
              if (res) {
                const educationList = [...this.data.educationList]
                educationList.splice(index, 1)
                this.setData({
                  educationList,
                  incompleteTips: this.calculateIncompleteTips(
                    educationList,
                    this.data.graduateStatus
                  ),
                })
                this.countIncomplete()
                wx.showToast({
                  title: "删除成功",
                  icon: "none",
                })
                // 通知父页面数据发生变化
                this.triggerEvent("dataChange", {
                  educationList: educationList,
                  graduateStatus: this.data.graduateStatus,
                })
              }
            })
            // 生产环境中的代码：
            // this.triggerEvent('deleteEducation', { index });
          }
        },
      })
    },

    // 选中学历卡片
    checkCard(e) {
      const { index } = e.currentTarget.dataset
      let educationList = this.data.educationList
      educationList.forEach((item, cindex) => {
        if (index == cindex) {
          item.checked = true
        } else {
          item.checked = false
        }
      })
      this.triggerEvent("dataChange", {
        educationList: educationList,
        graduateStatus: this.data.graduateStatus,
      })
    },

    /**
     * 编辑应届生状态
     */
    onEditGraduateStatus() {
      console.log("education-card 应届生身份点击事件触发")
      // 设置回显索引
      let selectedIndex = 0
      if (this.data.graduateStatus) {
        const graduateData = this.data.graduateColumns[0].values
        const index = graduateData.findIndex(
          (item) => item.value === this.data.graduateStatus
        )
        if (index >= 0) {
          selectedIndex = index
        }
      }
      this.setData({
        graduateSelectedIndex: selectedIndex,
        graduateShow: true,
      })
      // 设置picker的默认索引
      setTimeout(() => {
        const picker = this.selectComponent("#graduatePicker")
        if (picker && picker.setIndexes) {
          picker.setIndexes([selectedIndex])
        }
      }, 50)
    },

    /**
     * 关闭应届生身份选择器
     */
    onGraduateClose() {
      this.setData({
        graduateShow: false,
      })
    },

    /**
     * 确认应届生身份选择
     */
    onGraduateConfirm(e) {
      const { value, index } = e.detail
      if (!value || value.length === 0) return
      const selectedItem = value[0]
      const status = selectedItem.value
      const text = selectedItem.text
      this.setData({
        graduateStatus: status,
        graduateText: text,
        graduateShow: false,
        incompleteTips: this.calculateIncompleteTips(
          this.data.educationList,
          status
        ),
      })
      // 通知父页面数据发生变化
      this.triggerEvent("dataChange", {
        educationList: this.data.educationList,
        graduateStatus: status,
      })
      // 选择后刷新待完善项数
      this.countIncomplete()
    },

    /**
     * 滚动事件处理 - 用于更新指示器
     */
    onScroll(e) {
      const { scrollLeft } = e.detail
      // 每个项目的宽度约为 604rpx (580rpx + 24rpx margin)
      // 这里用 rpx 转换为 px 的近似比例
      const itemWidth = 302 // 大约 604rpx 转换为 px
      const currentIndex = Math.round(scrollLeft / itemWidth)

      if (currentIndex !== this.data.currentIndex) {
        this.setData({
          currentIndex: Math.min(
            currentIndex,
            this.data.educationList.length - 1
          ),
        })
      }
    },
  },
})
