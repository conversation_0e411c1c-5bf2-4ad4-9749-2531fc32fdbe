page {
  // background: rgba(247, 248, 250, 1);
  box-sizing: border-box;
}

.lefts {
  padding-left: 32rpx;
  display: flex;
  align-items: center;

  .left-arrow {
    width: 40rpx;
    height: 40rpx;
  }
}

.main-content {
  .header-top {
    padding: 26rpx 32rpx 40rpx 32rpx;
    background: #fff;
    border-bottom: 16rpx solid rgba(247, 248, 250, 1);

    .title {
      font-size: 36rpx;
      color: rgba(34, 36, 46, 1);
      line-height: 54rpx;
      font-weight: bold;
    }

    .label {
      display: flex;
      align-items: center;
      margin-top: 25rpx;

      .adress {
        font-size: 24rpx;
        color: rgba(145, 148, 153, 0.8);
      }

      .time {
        font-size: 24rpx;
        color: rgba(145, 148, 153, 0.8);
        padding-left: 32rpx;
        position: relative;
        &.no_after {
          padding-left: 0;
          &::after {
            display: none;
          }
        }

        &::after {
          position: absolute;
          display: block;
          content: " ";
          width: 1rpx;
          height: 24rpx;
          left: 16rpx;
          top: 50%;
          transform: translateY(-50%);
          background: rgba(235, 236, 240, 1);
        }
      }
    }
  }
}

.content-box {
  background: #fff;
  padding: 0rpx 32rpx 40rpx 32rpx;
  padding-bottom: 48rpx;
}

.content-center {
  .notice-box {
    background: rgba(247, 248, 250, 1);
    padding: 32rpx;
    border-radius: 16rpx;
    .notice-box-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 24rpx;
      border-bottom: 1rpx solid rgba(235, 236, 240, 1);
      transform: rotateZ(360deg);

      .left {
        .left-text {
          font-size: 28rpx;
          color: rgba(60, 61, 66, 1);
          margin-right: 16rpx;

          .num {
            font-weight: bold;
          }
        }
      }

      .status {
        font-size: 28rpx;
        color: rgba(19, 191, 128, 1);
        // font-weight: bold;
      }
    }

    .time-line {
      margin-top: 24rpx;

      &-item {
        display: flex;
        position: relative;
        padding-left: 24rpx;
        padding-bottom: 24rpx;

        &:last-child {
          padding-bottom: 0;
        }

        &.active {
          .dian {
            width: 7px;
            height: 7px;
            background: #fff;
            border: 2rpx solid rgba(230, 0, 3, 1);
            left: -2px;
            top: 10rpx;

            &::after {
              display: block;
              content: " ";
              position: absolute;
              width: 3px;
              height: 3px;
              background-color: rgba(230, 0, 3, 1);
              left: 50%;
              top: 50%;
              border-radius: 50%;
              transform: translate(-50%, -50%);
            }
          }

          .text-word {
            color: rgba(230, 0, 3, 1);
          }

          .time {
            color: rgba(230, 0, 3, 1);
          }
        }

        &::after {
          position: absolute;
          display: block;
          content: " ";
          width: 1px;
          height: 100%;
          border-left: 1px dashed rgba(194, 197, 204, 1);
          left: 2px;
          z-index: 1;
          top: 15rpx;
          box-sizing: border-box;
        }

        &:last-child {
          &::after {
            display: none;
          }
        }

        .text-word {
          font-size: 24rpx;
          color: rgba(60, 61, 66, 1);
        }

        .time {
          font-size: 24rpx;
          color: rgba(145, 148, 153, 1);
          padding-left: 16rpx;
          flex: 1;
          min-width: 0;
        }

        .dian {
          width: 5px;
          height: 5px;
          background-color: rgba(194, 197, 204, 1);
          border-radius: 50%;
          position: absolute;
          z-index: 2;
          left: 0;
          top: 15rpx;
        }
      }
    }

    .source {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24rpx;
      color: rgba(60, 61, 66, 1);
      margin-top: 8rpx;

      .time {
        color: rgba(68, 138, 255, 1);
      }

      image {
        width: 32rpx;
        height: 32rpx;
        display: block;
      }
    }
  }

  .text-box {
    margin-top: 30rpx;
    font-size: 32rpx;
    line-height: 64rpx;
    color: rgba(60, 61, 66, 1);

    &.collapsed {
      height: 200rpx;
      overflow: hidden;
    }
  }

  // 新的rich-text容器样式
  .rich-text-container {
    margin-top: 30rpx;
    font-size: 32rpx;
    line-height: 64rpx;
    color: rgba(60, 61, 66, 1);
    position: relative;

    &.collapsed {
      position: relative;
    }

    &.expanded {
      max-height: none !important;
    }

    // rich-text {
    //   display: block;
    //   word-wrap: break-word;
    //   word-break: break-all;
    // }
    rich-text {
      display: block;
      word-wrap: break-word;
      word-break: break-all;
      font-size: 32rpx !important;
    }
  }
}

.action-bar-box {
  display: flex;
  height: 142rpx;
  z-index: 998;
  position: relative;

  box-sizing: border-box;
}

.action-bar .button-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .button {
    width: calc(50% - 7rpx);
    background-color: var(--main-color);
    font-size: 30rpx;
    color: #fff;
    height: 84rpx;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .w100 {
    width: 100% !important;
  }

  .next-btn {
    background: #ffffff;
    border: 1rpx solid rgba(230, 0, 0, 0.6);
    transform: rotateZ(360deg);
    color: #e60000;
  }
}

.action-bar {
  z-index: 1;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 142rpx;
  background-color: #fff;
  box-sizing: border-box;
  border-top: 1rpx solid #ebecf0;
  transform: rotateZ(360deg);
  padding: 24rpx 40rpx 34rpx 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.bottom-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 40rpx;
  position: relative;

  .new-box {
    position: absolute;
    font-size: 18rpx;
    color: rgba(230, 0, 3, 1);
    background: rgba(230, 0, 3, 0.1);
    padding: 2rpx 10rpx;
    right: -14rpx;
    border-radius: 20rpx 20rpx 20rpx 4rpx;
    top: -5px;
  }

  image {
    width: 40rpx;
    height: 40rpx;
    margin-bottom: 4rpx;
  }

  font-size: 20rpx;
  color: rgba(145, 148, 153, 1);
}

.filter-button {
  background: rgba(236, 62, 51, 1);
  font-size: 28rpx;
  color: #fff;
  // flex: 1;
  // min-width: 0;
  width: 400rpx;
  height: 84rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.bottom-pos {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

.gradient-height {
  height: 60rpx;
  width: 100%;
  box-sizing: border-box;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 100%);
  position: absolute;
  bottom: 60rpx; // 调整位置以适配新的展开按钮位置
  left: 0;
  z-index: 1;
}

.pr-box {
  position: relative;
  padding-bottom: 20rpx;
}

.see-all {
  font-size: 32rpx;
  color: rgba(60, 61, 66, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  margin-top: 0rpx;
  padding-top: 32rpx;
  box-sizing: border-box;

  &.collapsed {
    image {
      transform: rotate(0deg);
    }
  }

  image {
    width: 32rpx;
    height: 32rpx;
    transform: rotate(180deg);
  }
}

.dians {
  margin: 0 5rpx;
}
